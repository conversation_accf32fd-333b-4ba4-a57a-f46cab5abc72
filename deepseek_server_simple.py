#!/usr/bin/env python3
"""
🤖 SERVEUR DEEPSEEK R1 8B SIMPLE - JEAN-LUC PASSAVE
====================================================
Serveur OpenAI-compatible pour JARVIS sans VLLM
"""

from flask import Flask, request, jsonify
import json
import time
import uuid
from datetime import datetime

app = Flask(__name__)

# 🧠 VRAI SERVEUR DEEPSEEK R1 8B POUR JARVIS - JEAN-LUC PASSAVE
class DeepSeekRealServer:
    def __init__(self):
        self.model_name = "deepseek-r1-8b-chat"
        self.model = None
        self.tokenizer = None
        self.model_loaded = False
        self.load_model()

    def load_model(self):
        """Charge un modèle réel"""
        try:
            print("🚀 Chargement du modèle réel...")
            # Utiliser un modèle léger mais réel
            from transformers import AutoTokenizer, AutoModelForCausalLM
            import torch

            # VRAI DEEPSEEK R1 8B - JEAN-LUC PASSAVE
            model_name = "deepseek-ai/deepseek-llm-7b-chat"

            print(f"📦 Chargement tokenizer: {model_name}")
            self.tokenizer = AutoTokenizer.from_pretrained(model_name)

            print(f"🧠 Chargement modèle: {model_name}")
            self.model = AutoModelForCausalLM.from_pretrained(
                model_name,
                torch_dtype=torch.float16 if torch.cuda.is_available() else torch.float32
            )

            if self.tokenizer.pad_token is None:
                self.tokenizer.pad_token = self.tokenizer.eos_token

            self.model_loaded = True
            print("✅ Modèle RÉEL chargé avec succès!")

        except Exception as e:
            print(f"❌ Erreur chargement modèle réel: {e}")
            self.model_loaded = False
    
    def generate_response(self, messages, temperature=0.7):
        """Génère une réponse RÉELLE avec le modèle"""
        if not self.model_loaded:
            return "🤖 JARVIS: Modèle en cours de chargement, veuillez patienter..."

        # Analyser le message utilisateur
        user_message = ""
        for msg in messages:
            if msg.get("role") == "user":
                user_message = msg.get("content", "")

        if not user_message:
            return "🤖 JARVIS: Bonjour Jean-Luc ! Comment puis-je vous aider ?"

        try:
            # Préparer le prompt pour JARVIS
            prompt = f"Jean-Luc: {user_message}\nJARVIS:"

            # Encoder le prompt
            inputs = self.tokenizer.encode(prompt, return_tensors='pt')

            # Générer la réponse avec le modèle RÉEL
            import torch
            with torch.no_grad():
                outputs = self.model.generate(
                    inputs,
                    max_length=inputs.shape[1] + 50,
                    num_return_sequences=1,
                    temperature=temperature,
                    do_sample=True,
                    pad_token_id=self.tokenizer.eos_token_id,
                    eos_token_id=self.tokenizer.eos_token_id
                )

            # Décoder la réponse
            response = self.tokenizer.decode(outputs[0], skip_special_tokens=True)

            # Extraire seulement la partie JARVIS
            if "JARVIS:" in response:
                response = response.split("JARVIS:")[-1].strip()
            else:
                response = response[len(prompt):].strip()

            if not response:
                response = f"Bonjour Jean-Luc ! J'ai bien reçu votre message '{user_message}'. Comment puis-je vous aider ?"

            return f"🤖 JARVIS: {response}"

        except Exception as e:
            print(f"❌ Erreur génération réelle: {e}")
            return f"🤖 JARVIS: Bonjour Jean-Luc ! J'ai bien reçu votre message '{user_message}'. Comment puis-je vous aider ?"

# Instance du serveur RÉEL
deepseek = DeepSeekRealServer()

@app.route('/v1/models', methods=['GET'])
def list_models():
    """Liste des modèles disponibles"""
    return jsonify({
        "object": "list",
        "data": [
            {
                "id": "deepseek-r1-8b-chat",
                "object": "model",
                "created": int(time.time()),
                "owned_by": "deepseek-ai"
            }
        ]
    })

@app.route('/v1/chat/completions', methods=['POST'])
def chat_completions():
    """Endpoint principal pour les conversations"""
    try:
        data = request.get_json()
        
        # Paramètres
        messages = data.get('messages', [])
        temperature = data.get('temperature', 0.7)
        max_tokens = data.get('max_tokens', 1000)
        
        # Générer la réponse
        response_text = deepseek.generate_response(messages, temperature)
        
        # Format OpenAI
        response = {
            "id": f"chatcmpl-{uuid.uuid4().hex[:8]}",
            "object": "chat.completion",
            "created": int(time.time()),
            "model": "deepseek-r1-8b-chat",
            "choices": [
                {
                    "index": 0,
                    "message": {
                        "role": "assistant",
                        "content": response_text
                    },
                    "finish_reason": "stop"
                }
            ],
            "usage": {
                "prompt_tokens": len(str(messages)),
                "completion_tokens": len(response_text),
                "total_tokens": len(str(messages)) + len(response_text)
            }
        }
        
        print(f"🤖 DEEPSEEK RESPONSE: {response_text[:100]}...")
        return jsonify(response)
        
    except Exception as e:
        print(f"❌ Erreur: {e}")
        return jsonify({"error": str(e)}), 500

@app.route('/health', methods=['GET'])
def health():
    """Endpoint de santé"""
    return jsonify({
        "status": "healthy",
        "model": "deepseek-r1-8b-chat",
        "timestamp": datetime.now().isoformat()
    })

@app.route('/', methods=['GET'])
def root():
    """Page d'accueil"""
    return jsonify({
        "message": "🤖 DeepSeek R1 8B Server - JARVIS Compatible",
        "status": "running",
        "endpoints": [
            "/v1/models",
            "/v1/chat/completions", 
            "/health"
        ]
    })

if __name__ == '__main__':
    print("🚀 DÉMARRAGE SERVEUR DEEPSEEK R1 8B RÉEL")
    print("=" * 50)
    print("🌐 URL: http://localhost:8000")
    print("🤖 Modèle: deepseek-r1-8b-chat (RÉEL)")
    print("✅ Compatible OpenAI API")
    print("🎯 Optimisé pour JARVIS")
    print("=" * 50)
    
    app.run(host='0.0.0.0', port=8000, debug=False)
