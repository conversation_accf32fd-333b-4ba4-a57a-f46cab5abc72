#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
🔒 JARVIS CODE LOCK MEMORY - MÉMOIRE VERROUILLÉE POUR CODE VALIDÉ
===============================================================
Jean-Luc <PERSON> - Système de persistance garantie pour code validé
Empêche la perte de code lors des nettoyages de mémoire thermique
"""

import json
import os
import datetime
from typing import Dict, Optional, List
import hashlib

class JarvisCodeLockMemory:
    """Système de mémoire verrouillée pour code validé par <PERSON><PERSON>Luc"""
    
    def __init__(self, lock_file="jarvis_code_lock.json"):
        self.lock_file = lock_file
        self.code_lock = {}
        self.metadata = {}
        self.load_locked_memory()
        
        print("🔒 JARVIS Code Lock Memory initialisé")
        print(f"📁 Fichier: {self.lock_file}")
        print(f"🧠 {len(self.code_lock)} codes verrouillés chargés")
    
    def load_locked_memory(self):
        """Charge la mémoire verrouillée depuis le fichier"""
        try:
            if os.path.exists(self.lock_file):
                with open(self.lock_file, 'r', encoding='utf-8') as f:
                    data = json.load(f)
                    self.code_lock = data.get('code_lock', {})
                    self.metadata = data.get('metadata', {})
                print(f"✅ Mémoire verrouillée chargée: {len(self.code_lock)} codes")
            else:
                print("📝 Nouveau fichier de mémoire verrouillée créé")
        except Exception as e:
            print(f"⚠️ Erreur chargement mémoire verrouillée: {e}")
            self.code_lock = {}
            self.metadata = {}
    
    def save_locked_memory(self):
        """Sauvegarde la mémoire verrouillée"""
        try:
            data = {
                'code_lock': self.code_lock,
                'metadata': self.metadata,
                'last_update': datetime.datetime.now().isoformat(),
                'total_locked_codes': len(self.code_lock)
            }
            
            with open(self.lock_file, 'w', encoding='utf-8') as f:
                json.dump(data, f, indent=2, ensure_ascii=False)
            
            print(f"💾 Mémoire verrouillée sauvegardée: {len(self.code_lock)} codes")
            return True
        except Exception as e:
            print(f"❌ Erreur sauvegarde mémoire verrouillée: {e}")
            return False
    
    def lock_code(self, name: str, code: str, description: str = "") -> str:
        """Verrouille un code dans la mémoire permanente"""
        
        # Générer un hash pour vérifier l'intégrité
        code_hash = hashlib.md5(code.encode()).hexdigest()
        
        # Métadonnées
        metadata = {
            'locked_date': datetime.datetime.now().isoformat(),
            'description': description,
            'code_hash': code_hash,
            'code_length': len(code),
            'locked_by': 'Jean-Luc Passave'
        }
        
        # Verrouiller le code
        self.code_lock[name] = code
        self.metadata[name] = metadata
        
        # Sauvegarder immédiatement
        self.save_locked_memory()
        
        print(f"🔒 Code '{name}' verrouillé définitivement")
        print(f"📝 Description: {description}")
        print(f"🔢 Hash: {code_hash[:8]}...")
        
        return f"🧠 Code '{name}' ancré définitivement dans la mémoire verrouillée."
    
    def get_locked_code(self, name: str) -> Optional[str]:
        """Récupère un code verrouillé"""
        if name in self.code_lock:
            code = self.code_lock[name]
            
            # Vérifier l'intégrité
            current_hash = hashlib.md5(code.encode()).hexdigest()
            stored_hash = self.metadata.get(name, {}).get('code_hash', '')
            
            if current_hash != stored_hash:
                print(f"⚠️ ALERTE: Intégrité compromise pour '{name}'!")
            
            print(f"🔓 Code '{name}' récupéré depuis mémoire verrouillée")
            return code
        
        print(f"❌ Code '{name}' non trouvé dans mémoire verrouillée")
        return None
    
    def unlock_code(self, name: str) -> bool:
        """Déverrouille un code (supprime de la mémoire verrouillée)"""
        if name in self.code_lock:
            del self.code_lock[name]
            if name in self.metadata:
                del self.metadata[name]
            
            self.save_locked_memory()
            print(f"🔓 Code '{name}' déverrouillé et supprimé")
            return True
        
        print(f"❌ Code '{name}' non trouvé pour déverrouillage")
        return False
    
    def list_locked_codes(self) -> List[Dict]:
        """Liste tous les codes verrouillés avec métadonnées"""
        codes_info = []
        
        for name, code in self.code_lock.items():
            metadata = self.metadata.get(name, {})
            info = {
                'name': name,
                'description': metadata.get('description', ''),
                'locked_date': metadata.get('locked_date', ''),
                'code_length': len(code),
                'code_hash': metadata.get('code_hash', '')[:8] + '...'
            }
            codes_info.append(info)
        
        return codes_info
    
    def restore_to_thermal_memory(self, name: str, thermal_memory: Dict) -> bool:
        """Restaure un code verrouillé vers la mémoire thermique"""
        if name in self.code_lock:
            thermal_memory[name] = self.code_lock[name]
            print(f"🔁 Code '{name}' restauré depuis mémoire verrouillée vers thermique")
            return True
        
        print(f"❌ Code '{name}' non trouvé pour restauration")
        return False
    
    def protect_thermal_cleanup(self, thermal_memory: Dict) -> Dict:
        """Protège les codes verrouillés lors du nettoyage thermique"""
        protected_memory = {}
        
        for key, value in thermal_memory.items():
            # Garder si c'est dans la mémoire verrouillée
            if key in self.code_lock:
                protected_memory[key] = value
                print(f"🛡️ Code '{key}' protégé du nettoyage")
            # Ou garder selon d'autres critères (récent, important, etc.)
            else:
                # Ici tu peux ajouter d'autres critères de protection
                protected_memory[key] = value
        
        return protected_memory
    
    def auto_restore_missing_codes(self, thermal_memory: Dict) -> int:
        """Restaure automatiquement les codes manquants en mémoire thermique"""
        restored_count = 0
        
        for name in self.code_lock.keys():
            if name not in thermal_memory:
                thermal_memory[name] = self.code_lock[name]
                restored_count += 1
                print(f"🔁 Code '{name}' auto-restauré en mémoire thermique")
        
        if restored_count > 0:
            print(f"✅ {restored_count} codes auto-restaurés")
        
        return restored_count
    
    def get_status(self) -> Dict:
        """Retourne le statut de la mémoire verrouillée"""
        total_size = sum(len(code) for code in self.code_lock.values())
        
        return {
            'total_locked_codes': len(self.code_lock),
            'total_size_bytes': total_size,
            'lock_file': self.lock_file,
            'file_exists': os.path.exists(self.lock_file),
            'codes': list(self.code_lock.keys())
        }

# Instance globale
jarvis_code_lock = JarvisCodeLockMemory()

# Fonctions d'interface simplifiées
def lock_code(name: str, code: str, description: str = "") -> str:
    """Interface simple pour verrouiller du code"""
    return jarvis_code_lock.lock_code(name, code, description)

def get_locked_code(name: str) -> Optional[str]:
    """Interface simple pour récupérer du code verrouillé"""
    return jarvis_code_lock.get_locked_code(name)

def list_locked_codes() -> List[Dict]:
    """Interface simple pour lister les codes verrouillés"""
    return jarvis_code_lock.list_locked_codes()

def protect_thermal_memory(thermal_memory: Dict) -> Dict:
    """Interface simple pour protéger la mémoire thermique"""
    return jarvis_code_lock.protect_thermal_cleanup(thermal_memory)

def auto_restore_codes(thermal_memory: Dict) -> int:
    """Interface simple pour auto-restaurer les codes"""
    return jarvis_code_lock.auto_restore_missing_codes(thermal_memory)

if __name__ == "__main__":
    print("🔒 TEST JARVIS CODE LOCK MEMORY")
    print("=" * 50)
    
    # Test de verrouillage
    test_code = """
def hello_jarvis():
    print("Bonjour Jean-Luc!")
    return "JARVIS opérationnel"
"""
    
    result = lock_code("test_function", test_code, "Fonction de test pour Jean-Luc")
    print(result)
    
    # Test de récupération
    retrieved = get_locked_code("test_function")
    print(f"Code récupéré: {len(retrieved) if retrieved else 0} caractères")
    
    # Test de liste
    codes = list_locked_codes()
    print(f"Codes verrouillés: {len(codes)}")
    
    # Statut
    status = jarvis_code_lock.get_status()
    print(f"Statut: {status}")
