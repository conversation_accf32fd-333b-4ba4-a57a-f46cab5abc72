const { app, BrowserWindow, Menu, dialog, shell, ipcMain } = require('electron');
const path = require('path');
const { spawn, exec } = require('child_process');
const fs = require('fs');

// 🚀 JARVIS ELECTRON LAUNCHER - Créé avec amour par <PERSON> pour <PERSON>Luc
// Application Electron pour lancer et gérer JARVIS DeepSeek R1 8B

let mainWindow;
let jarvisProcess = null;
let deepseekProcess = null;
let isJarvisRunning = false;

// Configuration
const JARVIS_PORT = 7867;  // Port du vrai dashboard JARVIS
const DEEPSEEK_PORT = 8000;
const JARVIS_URL = `http://localhost:${JARVIS_PORT}`;

function createWindow() {
    console.log('🖥️ Création de la fenêtre JARVIS...');

    // Créer la fenêtre principale
    mainWindow = new BrowserWindow({
        width: 1400,
        height: 900,
        minWidth: 1200,
        minHeight: 800,
        webPreferences: {
            nodeIntegration: true,
            contextIsolation: false,
            webSecurity: false
        },
        // icon: path.join(__dirname, 'assets', 'jarvis-icon.png'), // Icône désactivée temporairement
        title: 'JARVIS DeepSeek R1 8B - Launcher',
        titleBarStyle: 'default',
        show: true,  // Afficher immédiatement
        center: true,  // Centrer la fenêtre
        alwaysOnTop: false,  // Ne pas rester au premier plan
        focusable: true  // Permettre le focus
    });

    // Charger la page de lancement
    mainWindow.loadFile('jarvis_launcher.html');

    // Afficher la fenêtre quand elle est prête
    mainWindow.once('ready-to-show', () => {
        mainWindow.show();
        mainWindow.focus();  // Forcer le focus
        mainWindow.moveTop();  // Amener au premier plan
        // Ne pas démarrer automatiquement - juste vérifier le statut
        setTimeout(() => {
            checkJarvisStatus();
        }, 2000);
        console.log('🖥️ Fenêtre JARVIS affichée et focalisée');
    });

    // Gérer la fermeture
    mainWindow.on('closed', () => {
        stopAllProcesses();
        mainWindow = null;
    });

    // Menu de l'application
    createMenu();
}

function createMenu() {
    const template = [
        {
            label: 'JARVIS',
            submenu: [
                {
                    label: 'Démarrer JARVIS',
                    accelerator: 'CmdOrCtrl+S',
                    click: startJarvis
                },
                {
                    label: 'Arrêter JARVIS',
                    accelerator: 'CmdOrCtrl+Q',
                    click: stopJarvis
                },
                { type: 'separator' },
                {
                    label: 'Ouvrir Interface JARVIS',
                    accelerator: 'CmdOrCtrl+O',
                    click: () => shell.openExternal(JARVIS_URL)
                },
                { type: 'separator' },
                {
                    label: 'Quitter',
                    accelerator: process.platform === 'darwin' ? 'Cmd+Q' : 'Ctrl+Q',
                    click: () => {
                        stopAllProcesses();
                        app.quit();
                    }
                }
            ]
        },
        {
            label: 'Outils',
            submenu: [
                {
                    label: 'Logs JARVIS',
                    click: () => shell.openPath(path.join(__dirname, 'jarvis.log'))
                },
                {
                    label: 'Logs DeepSeek',
                    click: () => shell.openPath(path.join(__dirname, 'deepseek.log'))
                },
                {
                    label: 'Mémoire Thermique',
                    click: () => shell.openPath(path.join(__dirname, 'thermal_memory_persistent.json'))
                },
                { type: 'separator' },
                {
                    label: 'Dossier JARVIS',
                    click: () => shell.openPath(__dirname)
                }
            ]
        },
        {
            label: 'Aide',
            submenu: [
                {
                    label: 'À propos de JARVIS',
                    click: showAbout
                },
                {
                    label: 'Documentation',
                    click: () => shell.openExternal('https://github.com/deepseek-ai/DeepSeek-R1')
                }
            ]
        }
    ];

    const menu = Menu.buildFromTemplate(template);
    Menu.setApplicationMenu(menu);
}

function startJarvis() {
    if (isJarvisRunning) {
        dialog.showMessageBox(mainWindow, {
            type: 'info',
            title: 'JARVIS',
            message: 'JARVIS est déjà en cours d\'exécution',
            detail: `Interface disponible sur: ${JARVIS_URL}`
        });
        return;
    }

    // Vérifier les scripts disponibles et choisir le meilleur
    const optimizedScript = path.join(__dirname, 'demarrer_jarvis_optimise.sh');
    const simpleScript = path.join(__dirname, 'demarrer_jarvis_simple.sh');

    if (fs.existsSync(optimizedScript)) {
        console.log('🚀 Utilisation script optimisé');
        jarvisProcess = spawn('./demarrer_jarvis_optimise.sh', [], {
            cwd: __dirname,
            stdio: ['pipe', 'pipe', 'pipe']
        });
    } else if (fs.existsSync(simpleScript)) {
        console.log('🚀 Utilisation script simple');
        jarvisProcess = spawn('./demarrer_jarvis_simple.sh', [], {
            cwd: __dirname,
            stdio: ['pipe', 'pipe', 'pipe']
        });
    } else {
        console.log('🚀 Lancement direct Python');
        // Lancer directement l'interface Python
        jarvisProcess = spawn('python', ['jarvis_interface_propre.py'], {
            cwd: __dirname,
            stdio: ['pipe', 'pipe', 'pipe'],
            env: { ...process.env, PATH: `${__dirname}/venv_deepseek/bin:${process.env.PATH}` }
        });
    }

    jarvisProcess.stdout.on('data', (data) => {
        console.log(`JARVIS: ${data}`);
        if (mainWindow && !mainWindow.isDestroyed()) {
            mainWindow.webContents.send('jarvis-log', data.toString());
        }
    });

    jarvisProcess.stderr.on('data', (data) => {
        console.error(`JARVIS Error: ${data}`);
        if (mainWindow && !mainWindow.isDestroyed()) {
            mainWindow.webContents.send('jarvis-error', data.toString());
        }
    });

    jarvisProcess.on('close', (code) => {
        console.log(`JARVIS process exited with code ${code}`);
        isJarvisRunning = false;
        if (mainWindow && !mainWindow.isDestroyed()) {
            mainWindow.webContents.send('jarvis-stopped');
        }
    });

    jarvisProcess.on('error', (error) => {
        console.error(`JARVIS process error: ${error}`);
        isJarvisRunning = false;
        if (mainWindow && !mainWindow.isDestroyed()) {
            mainWindow.webContents.send('jarvis-error', `Erreur de démarrage: ${error.message}`);
        }
    });

    isJarvisRunning = true;
    if (mainWindow && !mainWindow.isDestroyed()) {
        mainWindow.webContents.send('jarvis-starting');
    }

    // Vérifier le démarrage après 10 secondes
    setTimeout(() => {
        checkJarvisStatus();
    }, 10000);
}

function stopJarvis() {
    if (jarvisProcess) {
        jarvisProcess.kill('SIGTERM');
        jarvisProcess = null;
    }

    // Arrêter aussi avec le script d'arrêt si il existe
    const stopScriptPath = path.join(__dirname, 'arreter_jarvis.sh');
    if (fs.existsSync(stopScriptPath)) {
        exec('./arreter_jarvis.sh', { cwd: __dirname }, (error, stdout, stderr) => {
            if (error) {
                console.error(`Erreur arrêt JARVIS: ${error}`);
            } else {
                console.log('JARVIS arrêté proprement');
            }
        });
    }

    isJarvisRunning = false;
    if (mainWindow && !mainWindow.isDestroyed()) {
        mainWindow.webContents.send('jarvis-stopped');
    }
}

function stopAllProcesses() {
    stopJarvis();
    if (deepseekProcess) {
        deepseekProcess.kill('SIGTERM');
        deepseekProcess = null;
    }
}

function checkJarvisStatus() {
    // Vérifier si JARVIS répond
    const http = require('http');
    const req = http.get(`http://localhost:${JARVIS_PORT}`, (res) => {
        isJarvisRunning = true;
        if (mainWindow && !mainWindow.isDestroyed()) {
            mainWindow.webContents.send('jarvis-running', JARVIS_URL);
        }
    });

    req.on('error', () => {
        isJarvisRunning = false;
        if (mainWindow && !mainWindow.isDestroyed()) {
            mainWindow.webContents.send('jarvis-stopped');
        }
    });

    req.setTimeout(5000, () => {
        req.destroy();
        isJarvisRunning = false;
        if (mainWindow && !mainWindow.isDestroyed()) {
            mainWindow.webContents.send('jarvis-stopped');
        }
    });
}

function showAbout() {
    dialog.showMessageBox(mainWindow, {
        type: 'info',
        title: 'À propos de JARVIS',
        message: 'JARVIS DeepSeek R1 8B',
        detail: `Version: 2.0.0
Créé avec amour par Claude pour Jean-Luc Passave

Fonctionnalités:
• Agent DeepSeek R1 8B
• Mémoire Thermique Continue
• Interface Gradio Complète
• Sauvegarde Automatique
• Multi-Agents Autonomes

© 2025 Jean-Luc Passave`
    });
}

// IPC Handlers
ipcMain.handle('start-jarvis', startJarvis);
ipcMain.handle('stop-jarvis', stopJarvis);
ipcMain.handle('check-status', checkJarvisStatus);
ipcMain.handle('open-jarvis', () => shell.openExternal(JARVIS_URL));
ipcMain.handle('open-logs', () => {
    const logPath = path.join(__dirname, 'jarvis.log');
    if (fs.existsSync(logPath)) {
        shell.openPath(logPath);
    } else {
        console.log('Fichier de logs non trouvé');
    }
});
ipcMain.handle('open-memory', () => {
    const memoryPath = path.join(__dirname, 'thermal_memory_persistent.json');
    if (fs.existsSync(memoryPath)) {
        shell.openPath(memoryPath);
    } else {
        console.log('Fichier de mémoire non trouvé');
    }
});
ipcMain.handle('open-folder', () => shell.openPath(__dirname));

// Événements de l'application
app.whenReady().then(createWindow);

app.on('window-all-closed', () => {
    stopAllProcesses();
    if (process.platform !== 'darwin') {
        app.quit();
    }
});

app.on('activate', () => {
    if (BrowserWindow.getAllWindows().length === 0) {
        createWindow();
    }
});

app.on('before-quit', () => {
    stopAllProcesses();
});

console.log('🚀 JARVIS Electron Launcher démarré');
