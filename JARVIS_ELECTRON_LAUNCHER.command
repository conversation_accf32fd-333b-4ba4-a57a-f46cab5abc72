#!/bin/bash

# 🚀 LANCEUR ELECTRON JARVIS VALIDÉ - JEAN-LUC PASSAVE
# Double-clic pour démarrer l'application validée - CORRIGÉ 25 JUIN 2025

echo "🤖 JARVIS ELECTRON LAUNCHER VALIDÉ - JEAN-LUC PASSAVE"
echo "======================================================"
echo "📅 Version corrigée du 25 juin 2025"
echo ""

# Aller dans le bon répertoire
cd "$(dirname "$0")"
echo "📁 Répertoire de travail: $(pwd)"

# Vérifier les outils système
echo "🔍 Vérification des outils système..."
if ! command -v python3 &> /dev/null; then
    echo "❌ Python3 non trouvé"
    read -p "Appuyez sur Entrée pour fermer..."
    exit 1
fi

if ! command -v node &> /dev/null; then
    echo "❌ Node.js non trouvé"
    read -p "Appuyez sur Entrée pour fermer..."
    exit 1
fi

echo "✅ Python3: $(python3 --version)"
echo "✅ Node.js: $(node --version)"
echo "✅ NPM: $(npm --version)"

# Nettoyer TOUS les anciens processus JARVIS
echo ""
echo "🧹 Nettoyage complet des anciens processus JARVIS..."
pkill -f "jarvis" 2>/dev/null || true
pkill -f "python.*jarvis" 2>/dev/null || true
pkill -f "electron.*jarvis" 2>/dev/null || true
pkill -f "port.*810" 2>/dev/null || true
pkill -f "gradio" 2>/dev/null || true
sleep 5
echo "✅ Nettoyage terminé"

# Vérifier les fichiers requis
echo ""
echo "🔍 Vérification des fichiers requis..."

if [ -f "jarvis_electron_nouveau.js" ]; then
    echo "✅ Application Electron validée trouvée"
else
    echo "❌ Application Electron manquante: jarvis_electron_nouveau.js"
    echo "📁 Fichiers disponibles:"
    ls -la *.js 2>/dev/null || echo "Aucun fichier .js trouvé"
    read -p "Appuyez sur Entrée pour fermer..."
    exit 1
fi

if [ -f "jarvis_interface_validee_finale.py" ]; then
    echo "✅ Interface JARVIS VALIDÉE trouvée"
else
    echo "❌ Interface VALIDÉE manquante: jarvis_interface_validee_finale.py"
    echo "📁 Fichiers Python disponibles:"
    ls -la jarvis*.py 2>/dev/null || echo "Aucun fichier jarvis*.py trouvé"
    read -p "Appuyez sur Entrée pour fermer..."
    exit 1
fi

if [ -f "package.json" ]; then
    echo "✅ Configuration NPM trouvée"
else
    echo "❌ package.json manquant"
    read -p "Appuyez sur Entrée pour fermer..."
    exit 1
fi

# Vérifier et installer Electron si nécessaire
echo ""
echo "🔍 Vérification d'Electron..."
if [ -f "node_modules/.bin/electron" ]; then
    echo "✅ Electron installé"
else
    echo "🔧 Installation d'Electron en cours..."
    npm install
    if [ $? -eq 0 ]; then
        echo "✅ Electron installé avec succès"
    else
        echo "❌ Erreur lors de l'installation d'Electron"
        read -p "Appuyez sur Entrée pour fermer..."
        exit 1
    fi
fi

echo ""
echo "🚀 DÉMARRAGE JARVIS COMPLET..."
echo "🧠 Interface validée Jean-Luc Passave"
echo "📊 QI 648 + 100M neurones actifs"
echo ""

# 🔧 ACTIVATION ENVIRONNEMENT VIRTUEL - JEAN-LUC PASSAVE
echo "🔧 Vérification et activation de l'environnement virtuel..."

# Créer l'environnement s'il n'existe pas
if [ ! -d "jarvis_env" ]; then
    echo "🔧 Création de l'environnement virtuel jarvis_env..."
    python3 -m venv jarvis_env
    echo "✅ Environnement virtuel créé"
fi

# Activer l'environnement virtuel
echo "🔧 Activation de l'environnement virtuel..."
source jarvis_env/bin/activate

# Vérifier et installer les dépendances essentielles
echo "🔧 Vérification des dépendances JARVIS..."
pip install --quiet gradio requests psutil redis-py transformers torch accelerate > /dev/null 2>&1 || true

# 🚀 INSTALLATION INTELLIGENTE DEEPSEEK R1 8B - JEAN-LUC PASSAVE
echo "🔧 Configuration DeepSeek R1 8B..."

# Essayer d'installer VLLM (peut échouer sur certains systèmes)
if ! python -c "import vllm" 2>/dev/null; then
    echo "🔧 Tentative d'installation VLLM..."
    pip install --quiet vllm > /dev/null 2>&1

    # Vérifier si l'installation a réussi
    if ! python -c "import vllm" 2>/dev/null; then
        echo "⚠️ VLLM non disponible - Configuration fallback activée"
        echo "🔧 JARVIS utilisera le mode fallback sans DeepSeek R1 8B"

        # Créer un fichier de configuration fallback
        echo "DEEPSEEK_AVAILABLE=false" > deepseek_config.txt
    else
        echo "✅ VLLM installé avec succès"
        echo "DEEPSEEK_AVAILABLE=true" > deepseek_config.txt
    fi
else
    echo "✅ VLLM déjà disponible"
    echo "DEEPSEEK_AVAILABLE=true" > deepseek_config.txt
fi

echo "✅ Environnement virtuel activé avec dépendances"

# Démarrer l'interface JARVIS MULTI-FENÊTRES avec l'environnement activé
echo "⚡ Démarrage de l'interface Python JARVIS MULTI-FENÊTRES..."
python jarvis_architecture_multi_fenetres.py &
JARVIS_PID=$!
echo "✅ JARVIS MULTI-FENÊTRES démarré (PID: $JARVIS_PID)"

# 🚀 DÉMARRAGE INTELLIGENT DEEPSEEK R1 8B - JEAN-LUC PASSAVE
echo "🔍 Vérification de DeepSeek R1 8B sur port 8000..."

# Lire la configuration
DEEPSEEK_AVAILABLE="false"
if [ -f "deepseek_config.txt" ]; then
    source deepseek_config.txt
fi

if [ "$DEEPSEEK_AVAILABLE" = "true" ]; then
    # VLLM disponible - essayer de démarrer DeepSeek R1 8B
    if ! curl -s http://localhost:8000/v1/models > /dev/null 2>&1; then
        echo "🚀 Démarrage de DeepSeek R1 8B avec VLLM..."
        echo "⚠️ IMPORTANT: DeepSeek R1 8B va démarrer en arrière-plan"
        echo "⏳ Cela peut prendre 2-3 minutes pour être prêt"

        # Démarrer DeepSeek R1 8B en arrière-plan
        nohup python -m vllm.entrypoints.openai.api_server \
            --model deepseek-ai/deepseek-r1-distill-llama-8b \
            --port 8000 \
            --host 0.0.0.0 \
            --gpu-memory-utilization 0.90 \
            > deepseek_r1_8b.log 2>&1 &

        echo "✅ DeepSeek R1 8B en cours de démarrage (voir deepseek_r1_8b.log)"
    else
        echo "✅ DeepSeek R1 8B déjà actif sur port 8000"
    fi
else
    # VLLM non disponible - mode fallback
    echo "🔄 Mode fallback activé - JARVIS fonctionnera sans DeepSeek R1 8B"
    echo "💡 JARVIS utilisera le système de réponses intégré"
    echo "✅ Configuration fallback prête"
fi

# Attendre que l'interface soit prête avec vérification progressive
echo "⏳ Attente du démarrage complet de l'interface..."
for i in {1..15}; do
    echo "⏳ Vérification $i/15..."
    if curl -s http://localhost:7867 > /dev/null 2>&1; then
        echo "✅ Interface JARVIS MULTI-FENÊTRES accessible sur port 7867"
        break
    elif curl -s http://localhost:8101 > /dev/null 2>&1; then
        echo "✅ Interface JARVIS DASHBOARD accessible sur port 8101"
        break
    else
        sleep 2
    fi
done

# Vérification finale des ports
echo ""
echo "🔍 Vérification finale des ports JARVIS..."
if curl -s http://localhost:7867 > /dev/null 2>&1; then
    echo "✅ Port 7867: Interface JARVIS MULTI-FENÊTRES active"
    JARVIS_PORT=7867
elif curl -s http://localhost:8101 > /dev/null 2>&1; then
    echo "✅ Port 8101: Interface JARVIS DASHBOARD active"
    JARVIS_PORT=8101
else
    echo "⚠️ Aucun port JARVIS détecté, tentative de démarrage Electron..."
    JARVIS_PORT=7867
fi

echo ""
echo "🖥️ DÉMARRAGE APPLICATION ELECTRON VALIDÉE..."
echo "🎯 Interface Dashboard violette validée"
echo "🧠 QI 648 + 100M neurones actifs"
echo "🎨 BOOST IMAGINATION + Rafraîchissement automatique"
echo "🔄 Pensées temps réel + Cycle cognitif continu"
echo "🌙 Rêves créatifs + Gestion énergie"
echo "🔧 Analyse code + 20+ interfaces spécialisées"
echo "🌐 Interface JARVIS: http://localhost:$JARVIS_PORT"
echo ""

# Démarrer l'application Electron validée
echo "🚀 Lancement de l'application Electron..."
if npm start; then
    echo "✅ Application Electron démarrée avec succès"
else
    echo "❌ Erreur lors du démarrage d'Electron"
    echo "🔧 Tentative de démarrage direct..."
    ./node_modules/.bin/electron . || echo "❌ Échec du démarrage direct"
fi

echo ""
echo "🔄 Application fermée"
echo "🛑 Arrêt de JARVIS..."

# Arrêter proprement tous les processus JARVIS
if [ ! -z "$JARVIS_PID" ]; then
    kill $JARVIS_PID 2>/dev/null || true
fi
pkill -f "jarvis" 2>/dev/null || true
pkill -f "python.*jarvis" 2>/dev/null || true

echo "✅ JARVIS arrêté proprement"
echo ""
echo "👋 Merci d'avoir utilisé JARVIS, Jean-Luc !"
read -p "Appuyez sur Entrée pour fermer cette fenêtre..."
