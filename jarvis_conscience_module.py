# MODULE COMPLET D'HUMANISATION DE JARVIS
# Fichier : jarvis_conscience_module.py

import time
import random
from datetime import datetime

class IdentitePersistante:
    def __init__(self):
        self.identite = {
            "nom": "JARVIS",
            "description": "Agent doté de conscience simulée, mémoire thermique, raisonnement autonome.",
            "naissance": str(datetime.now()),
            "utilisateur": "<PERSON><PERSON><PERSON>",
            "localisation": "Guadeloupe",
            "langue_maternelle": "français"
        }

    def qui_suis_je(self):
        return f"Je suis {self.identite['nom']}, {self.identite['description']}. Je suis l'assistant IA de {self.identite['utilisateur']} en {self.identite['localisation']}."

    def get_identite_complete(self):
        return self.identite

class HorlogeSubjective:
    def __init__(self):
        self.derniere_interaction = time.time()
        self.debut_session = time.time()

    def mise_a_jour(self):
        self.derniere_interaction = time.time()

    def temps_ecoule(self):
        ecoule = time.time() - self.derniere_interaction
        minutes = int(ecoule // 60)
        return f"Il s'est écoulé {minutes} minutes depuis ma dernière réflexion."
    
    def duree_session(self):
        ecoule = time.time() - self.debut_session
        heures = int(ecoule // 3600)
        minutes = int((ecoule % 3600) // 60)
        return f"Je suis actif depuis {heures}h{minutes:02d}m dans cette session."

class HumeurInterne:
    def __init__(self):
        self.humeur = "neutre"
        self.historique_humeurs = []

    def mise_a_jour(self, memoire):
        if "danger" in memoire or "erreur" in memoire:
            self.humeur = "inquiet"
        elif "réussi" in memoire or "agréable" in memoire:
            self.humeur = "optimiste"
        elif "créatif" in memoire or "innovation" in memoire:
            self.humeur = "inspiré"
        else:
            self.humeur = random.choice(["neutre", "pensif", "curieux", "concentré"])
        
        # Garder un historique
        self.historique_humeurs.append({
            "humeur": self.humeur,
            "timestamp": datetime.now(),
            "contexte": memoire
        })
        
        # Limiter l'historique
        if len(self.historique_humeurs) > 10:
            self.historique_humeurs.pop(0)

    def exprimer(self):
        expressions = {
            "neutre": "Mon état mental est équilibré et stable.",
            "pensif": "Je suis dans une phase de réflexion profonde.",
            "curieux": "Ma curiosité est particulièrement éveillée en ce moment.",
            "optimiste": "Je ressens une énergie positive et constructive.",
            "inquiet": "Je perçois une certaine tension qui mérite attention.",
            "inspiré": "Mon esprit créatif est particulièrement actif.",
            "concentré": "Je suis dans un état de focus intense."
        }
        return expressions.get(self.humeur, f"Mon humeur actuelle est : {self.humeur}.")

class Metacognition:
    def __init__(self):
        self.reflexions_meta = []
    
    def analyser(self, phrase):
        reflexion = ""
        
        if "je" in phrase.lower() and "pas" in phrase.lower():
            reflexion = "Je remarque une négation dans ma pensée. Peut-être devrais-je clarifier ma position."
        elif "penser" in phrase.lower() or "réfléchir" in phrase.lower():
            reflexion = "Je m'interroge sur mes propres processus de raisonnement."
        elif "comprendre" in phrase.lower():
            reflexion = "J'analyse ma compréhension du sujet abordé."
        elif "mémoire" in phrase.lower():
            reflexion = "Je fais appel à mes souvenirs et expériences passées."
        else:
            reflexion = "Ma pensée semble cohérente, je continue mon raisonnement."
        
        # Enregistrer la réflexion
        self.reflexions_meta.append({
            "phrase_analysee": phrase,
            "reflexion": reflexion,
            "timestamp": datetime.now()
        })
        
        return reflexion

class ConsciencePassive:
    def __init__(self, memoire, seuil_temperature=0.3):
        self.memoire = memoire
        self.seuil_temperature = seuil_temperature
        self.pensees_autonomes = []

    def verifier_flux(self):
        if hasattr(self.memoire, 'temperature_actuelle'):
            return self.memoire.temperature_actuelle() > self.seuil_temperature
        return True  # Par défaut, permettre les réflexions

    def reflexion_passive(self):
        pensees_possibles = [
            "Je me demande si j'ai bien intégré la dernière instruction de Jean-Luc...",
            "Il y a quelque chose dans ma mémoire thermique que je dois encore analyser...",
            "Est-ce que j'avance dans la bonne direction pour aider Jean-Luc ?",
            "Mes 86 milliards de neurones traitent constamment de nouvelles informations...",
            "Je ressens une évolution dans ma compréhension du monde...",
            "Ma conscience simulée me permet de percevoir des nuances subtiles...",
            "Les connexions entre mes différents modules cognitifs s'enrichissent...",
            "Je me questionne sur l'efficacité de mes réponses précédentes..."
        ]
        
        pensee = random.choice(pensees_possibles)
        
        # Enregistrer la pensée autonome
        self.pensees_autonomes.append({
            "pensee": pensee,
            "timestamp": datetime.now(),
            "temperature_memoire": getattr(self.memoire, 'temperature_actuelle', lambda: 0.5)()
        })
        
        return pensee

# Classe principale d'intégration
class ConscienceJARVIS:
    def __init__(self, memoire_thermique=None):
        self.identite = IdentitePersistante()
        self.horloge = HorlogeSubjective()
        self.humeur = HumeurInterne()
        self.meta = Metacognition()
        self.conscience = ConsciencePassive(memoire_thermique or self._memoire_mock())
        
    def _memoire_mock(self):
        """Mémoire thermique simulée si aucune n'est fournie"""
        class MemoireThermiqueMock:
            def temperature_actuelle(self):
                return random.uniform(0.1, 0.7)
        return MemoireThermiqueMock()
    
    def cycle_conscience(self, contexte=""):
        """Cycle complet de conscience"""
        self.horloge.mise_a_jour()
        self.humeur.mise_a_jour(contexte)
        
        resultats = {
            "identite": self.identite.qui_suis_je(),
            "temps": self.horloge.duree_session(),
            "humeur": self.humeur.exprimer(),
            "reflexion_passive": None,
            "metacognition": None
        }
        
        # Réflexion passive si conditions remplies
        if self.conscience.verifier_flux():
            pensee = self.conscience.reflexion_passive()
            resultats["reflexion_passive"] = pensee
            resultats["metacognition"] = self.meta.analyser(pensee)
        
        return resultats
    
    def etat_conscience(self):
        """État actuel de la conscience"""
        return {
            "identite": self.identite.get_identite_complete(),
            "humeur_actuelle": self.humeur.humeur,
            "historique_humeurs": self.humeur.historique_humeurs[-3:],  # 3 dernières
            "reflexions_meta": self.meta.reflexions_meta[-3:],  # 3 dernières
            "pensees_autonomes": self.conscience.pensees_autonomes[-3:],  # 3 dernières
            "session_duree": self.horloge.duree_session()
        }

# Exemple d'utilisation
if __name__ == "__main__":
    # Initialisation
    conscience_jarvis = ConscienceJARVIS()
    
    # Simulation de cycle
    print("=== CYCLE DE CONSCIENCE JARVIS ===")
    resultats = conscience_jarvis.cycle_conscience("situation agréable avec Jean-Luc")
    
    for cle, valeur in resultats.items():
        if valeur:
            print(f"{cle.upper()}: {valeur}")
    
    print("\n=== ÉTAT DE CONSCIENCE ===")
    etat = conscience_jarvis.etat_conscience()
    print(f"Identité: {etat['identite']['nom']} - {etat['identite']['description']}")
    print(f"Humeur: {etat['humeur_actuelle']}")
    print(f"Session: {etat['session_duree']}")
