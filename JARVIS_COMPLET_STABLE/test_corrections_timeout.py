#!/usr/bin/env python3
"""
🔧 TEST CORRECTIONS TIMEOUT MÉMOIRE THERMIQUE NATURELLE
Test des optimisations pour Jean-Luc Passave
Vérifie que les timeouts sont corrigés
"""

import sys
import os
import time

# Ajouter le répertoire au path
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from integration_memoire_naturelle_jarvis import (
    initialiser_memoire_naturelle_jarvis,
    traiter_message_avec_memoire_naturelle,
    obtenir_stats_memoire_pour_interface,
    integrateur_memoire
)

def test_corrections_timeout():
    """Test des corrections de timeout"""
    print("🔧 TEST CORRECTIONS TIMEOUT MÉMOIRE THERMIQUE")
    print("=" * 60)
    
    # 1. Initialisation
    print("\n🚀 Initialisation avec corrections...")
    integrateur = initialiser_memoire_naturelle_jarvis()
    
    if not integrateur:
        print("❌ Échec initialisation")
        return False
    
    # 2. Test mode turbo optimisé
    print("\n🚀 Test mode turbo optimisé...")
    integrateur.activer_mode_turbo(True)
    
    messages_turbo = [
        "Salut JARVIS !",
        "Comment ça va ?", 
        "Parle-moi rapidement de toi",
        "Merci !"
    ]
    
    for i, message in enumerate(messages_turbo, 1):
        print(f"\n⚡ Test turbo {i}: {message}")
        
        debut = time.time()
        try:
            reponse = traiter_message_avec_memoire_naturelle(message)
            duree = time.time() - debut
            
            print(f"🤖 JARVIS ({duree:.2f}s): {reponse[:150]}...")
            
            if duree > 20:  # Si plus de 20 secondes
                print("⚠️ Réponse lente détectée")
            else:
                print("✅ Réponse dans les temps")
                
        except Exception as e:
            duree = time.time() - debut
            print(f"⚠️ Erreur ({duree:.2f}s): {e}")
        
        time.sleep(1)  # Pause courte
    
    # Désactiver turbo
    integrateur.activer_mode_turbo(False)
    
    # 3. Test mode normal optimisé
    print("\n🔄 Test mode normal optimisé...")
    
    messages_normaux = [
        "JARVIS, peux-tu me donner une réponse détaillée sur tes capacités ?",
        "Explique-moi ton système de mémoire thermique"
    ]
    
    for i, message in enumerate(messages_normaux, 1):
        print(f"\n📝 Test normal {i}: {message}")
        
        debut = time.time()
        try:
            reponse = traiter_message_avec_memoire_naturelle(message)
            duree = time.time() - debut
            
            print(f"🤖 JARVIS ({duree:.2f}s): {reponse[:200]}...")
            
            if duree > 70:  # Si plus de 70 secondes
                print("⚠️ Timeout probable")
            else:
                print("✅ Réponse dans les temps")
                
        except Exception as e:
            duree = time.time() - debut
            print(f"⚠️ Erreur ({duree:.2f}s): {e}")
        
        time.sleep(2)
    
    # 4. Vérification mémoire après corrections
    print("\n📊 Vérification mémoire après corrections...")
    
    try:
        stats = obtenir_stats_memoire_pour_interface()
        
        if 'stats_memoire' in stats:
            mem_stats = stats['stats_memoire']
            print(f"🔥 Zone Hot: {mem_stats.get('zone_hot', 0)} entrées")
            print(f"🌡️ Zone Warm: {mem_stats.get('zone_warm', 0)} entrées")
            print(f"💭 Pensées autonomes: {mem_stats.get('pensees_autonomes', 0)}")
            print(f"🧠 Flux conscience: {'✅' if mem_stats.get('flux_conscience_actif', False) else '❌'}")
            
            # Vérifier que la mémoire a bien évolué
            total_entrees = mem_stats.get('zone_hot', 0) + mem_stats.get('zone_warm', 0)
            if total_entrees > 0:
                print("✅ Mémoire thermique fonctionne malgré les timeouts")
            else:
                print("⚠️ Mémoire thermique vide")
        
    except Exception as e:
        print(f"⚠️ Erreur vérification mémoire: {e}")
    
    # 5. Test récupération après timeout
    print("\n🔄 Test récupération après timeout...")
    
    try:
        # Forcer un message complexe qui pourrait causer timeout
        message_complexe = "JARVIS, peux-tu me faire une analyse complète et détaillée de l'intelligence artificielle, ses implications philosophiques, éthiques, et son impact sur l'humanité, en prenant en compte tous les aspects techniques, sociaux, économiques et culturels ?"
        
        print("📝 Test message complexe (risque de timeout)...")
        
        debut = time.time()
        reponse = traiter_message_avec_memoire_naturelle(message_complexe)
        duree = time.time() - debut
        
        print(f"🤖 JARVIS ({duree:.2f}s): {reponse[:300]}...")
        
        if "mémoire thermique" in reponse.lower() or "réflexion" in reponse.lower():
            print("✅ Récupération intelligente basée sur la mémoire")
        else:
            print("✅ Réponse générée normalement")
            
    except Exception as e:
        print(f"⚠️ Erreur test complexe: {e}")
    
    # 6. Rapport final des corrections
    print("\n📋 Rapport final des corrections...")
    
    try:
        rapport = integrateur.generer_rapport_memoire()
        print(rapport[:500] + "...")
        
    except Exception as e:
        print(f"⚠️ Erreur rapport: {e}")
    
    print("\n" + "=" * 60)
    print("✅ TEST CORRECTIONS TIMEOUT TERMINÉ")
    
    return True

def test_performance_continue():
    """Test de performance en continu"""
    print("\n⚡ TEST PERFORMANCE CONTINUE")
    print("-" * 40)
    
    messages_rapides = [
        "Oui",
        "Non", 
        "Peut-être",
        "D'accord",
        "Merci",
        "Bonjour",
        "Au revoir",
        "Comment ça va ?",
        "Très bien",
        "Parfait"
    ]
    
    temps_total = 0
    reponses_reussies = 0
    
    for i, message in enumerate(messages_rapides, 1):
        print(f"⚡ Test rapide {i}/10: {message}")
        
        debut = time.time()
        try:
            reponse = traiter_message_avec_memoire_naturelle(message)
            duree = time.time() - debut
            temps_total += duree
            reponses_reussies += 1
            
            print(f"✅ ({duree:.2f}s) {reponse[:50]}...")
            
        except Exception as e:
            duree = time.time() - debut
            temps_total += duree
            print(f"❌ ({duree:.2f}s) {e}")
        
        time.sleep(0.5)  # Pause très courte
    
    # Statistiques finales
    if reponses_reussies > 0:
        temps_moyen = temps_total / len(messages_rapides)
        taux_reussite = (reponses_reussies / len(messages_rapides)) * 100
        
        print(f"\n📊 STATISTIQUES PERFORMANCE:")
        print(f"⏱️ Temps moyen: {temps_moyen:.2f}s")
        print(f"✅ Taux de réussite: {taux_reussite:.1f}%")
        print(f"🎯 Réponses réussies: {reponses_reussies}/{len(messages_rapides)}")
        
        if temps_moyen < 10:
            print("🚀 Performance EXCELLENTE")
        elif temps_moyen < 20:
            print("✅ Performance BONNE")
        else:
            print("⚠️ Performance à améliorer")

if __name__ == "__main__":
    print("🔧 DÉMARRAGE TESTS CORRECTIONS TIMEOUT")
    
    # Test principal des corrections
    if test_corrections_timeout():
        
        # Test performance si demandé
        reponse = input("\n🤔 Voulez-vous tester la performance continue ? (oui/non): ").lower()
        
        if reponse in ['oui', 'o', 'yes', 'y']:
            test_performance_continue()
        
        print("\n🎉 TOUS LES TESTS DE CORRECTIONS TERMINÉS !")
        
    else:
        print("\n❌ ÉCHEC DES TESTS DE CORRECTIONS")
