#!/usr/bin/env python3
"""
🚀 DÉMARRAGE JARVIS AVEC MÉMOIRE THERMIQUE NATURELLE
Script de démarrage pour Jean-Luc Passave
"""

import os
import sys

# Ajouter le répertoire au path
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

def demarrer_jarvis_memoire_naturelle():
    """Démarre JARVIS avec mémoire thermique naturelle"""
    print("🚀 Démarrage JARVIS avec Mémoire Thermique Naturelle")
    
    try:
        # Importer et initialiser
        from integration_memoire_naturelle_jarvis import initialiser_memoire_naturelle_jarvis
        
        integrateur = initialiser_memoire_naturelle_jarvis()
        
        if integrateur:
            print("✅ JARVIS avec Mémoire Naturelle prêt")
            
            # Démarrer l'interface principale
            from jarvis_architecture_multi_fenetres import main
            main()
        else:
            print("❌ Échec initialisation mémoire naturelle")
            
    except Exception as e:
        print(f"❌ Erreur démarrage: {e}")

if __name__ == "__main__":
    demarrer_jarvis_memoire_naturelle()
