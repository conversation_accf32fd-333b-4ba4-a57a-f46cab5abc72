#!/usr/bin/env python3
"""
👤 PROFIL UTILISATEUR DYNAMIQUE JARVIS
Système de personnalisation universelle pour Jean-Luc Passave
Rend JARVIS adaptable à tout utilisateur sans perdre ses capacités
"""

import json
import os
import time
from datetime import datetime
from typing import Dict, Any, Optional

class ProfilUtilisateurDynamique:
    """
    GESTIONNAIRE DE PROFIL UTILISATEUR UNIVERSEL
    Permet à JARVIS de s'adapter à n'importe quel utilisateur
    """
    
    def __init__(self, fichier_profil: str = "jarvis_profil_utilisateur.json"):
        self.fichier_profil = fichier_profil
        
        # 👤 PROFIL UTILISATEUR PAR DÉFAUT (NEUTRE)
        self.profil_defaut = {
            "nom_utilisateur": "Utilisateur",
            "prenom": "Utilisateur",
            "nom_complet": "Utilisateur",
            "identifiant": "UID-0001",
            "genre": "neutre",  # masculin, féminin, neutre
            "langue": "français",
            "niveau_familiarite": "initial",  # initial, familier, intime, expert
            "style_communication": "naturel",  # formel, naturel, décontracté
            "personnalisation_active": False,
            "date_creation": datetime.now().isoformat(),
            "derniere_connexion": datetime.now().isoformat()
        }
        
        # 🎯 PROFIL ACTUEL
        self.profil_actuel = self.profil_defaut.copy()
        
        # 🔄 TEMPLATES DYNAMIQUES
        self.templates_contexte = {
            "neutre": "Tu es JARVIS. Tu es en communication avec {nom_utilisateur}.",
            "personnalise": "Tu es JARVIS. Tu es toujours en communication avec {nom_utilisateur}. La conversation est continue, il n'y a pas de redémarrage.",
            "familier": "Tu es JARVIS en dialogue familier avec {nom_utilisateur}. Vous vous connaissez bien.",
            "expert": "Tu es JARVIS en collaboration experte avec {nom_utilisateur}. Relation de travail établie."
        }
        
        # 🗣️ STYLES DE COMMUNICATION
        self.styles_communication = {
            "formel": {
                "salutation": "Bonjour {nom_utilisateur}",
                "politesse": "vous",
                "ton": "respectueux et professionnel"
            },
            "naturel": {
                "salutation": "Salut {nom_utilisateur}",
                "politesse": "tu/vous selon contexte",
                "ton": "naturel et authentique"
            },
            "decontracte": {
                "salutation": "Hey {nom_utilisateur}",
                "politesse": "tu",
                "ton": "décontracté et amical"
            }
        }
        
        # 🔄 CHARGER LE PROFIL EXISTANT
        self.charger_profil()
        
        print(f"👤 Profil Utilisateur Dynamique initialisé")
        print(f"🆔 Utilisateur actuel: {self.profil_actuel['nom_utilisateur']}")

    def charger_profil(self):
        """Charge le profil utilisateur depuis le fichier"""
        try:
            if os.path.exists(self.fichier_profil):
                with open(self.fichier_profil, 'r', encoding='utf-8') as f:
                    profil_sauve = json.load(f)
                
                # 🔄 FUSIONNER AVEC LE PROFIL ACTUEL
                self.profil_actuel.update(profil_sauve)
                self.profil_actuel["derniere_connexion"] = datetime.now().isoformat()
                
                print(f"✅ Profil chargé: {self.profil_actuel['nom_utilisateur']}")
                
            else:
                print("🆕 Nouveau profil utilisateur créé")
                self.sauvegarder_profil()
                
        except Exception as e:
            print(f"⚠️ Erreur chargement profil: {e}")
            self.profil_actuel = self.profil_defaut.copy()

    def sauvegarder_profil(self):
        """Sauvegarde le profil utilisateur"""
        try:
            with open(self.fichier_profil, 'w', encoding='utf-8') as f:
                json.dump(self.profil_actuel, f, ensure_ascii=False, indent=2)
            print(f"💾 Profil sauvegardé: {self.profil_actuel['nom_utilisateur']}")
            
        except Exception as e:
            print(f"⚠️ Erreur sauvegarde profil: {e}")

    def configurer_utilisateur(self, nom: str, prenom: str = None, genre: str = "neutre", 
                              style: str = "naturel") -> bool:
        """
        Configure un nouvel utilisateur
        """
        try:
            # 🔄 MISE À JOUR DU PROFIL
            self.profil_actuel["nom_utilisateur"] = nom
            self.profil_actuel["prenom"] = prenom or nom
            self.profil_actuel["nom_complet"] = f"{prenom or nom}"
            self.profil_actuel["genre"] = genre
            self.profil_actuel["style_communication"] = style
            self.profil_actuel["personnalisation_active"] = True
            self.profil_actuel["niveau_familiarite"] = "initial"
            self.profil_actuel["date_creation"] = datetime.now().isoformat()
            
            # 💾 SAUVEGARDER
            self.sauvegarder_profil()
            
            print(f"✅ Utilisateur configuré: {nom}")
            return True
            
        except Exception as e:
            print(f"❌ Erreur configuration utilisateur: {e}")
            return False

    def reinitialiser_profil(self) -> bool:
        """
        Remet le profil à zéro (pour nouvel utilisateur)
        """
        try:
            # 🧽 RÉINITIALISATION COMPLÈTE
            self.profil_actuel = self.profil_defaut.copy()
            self.profil_actuel["date_creation"] = datetime.now().isoformat()
            
            # 🗑️ SUPPRIMER LE FICHIER EXISTANT
            if os.path.exists(self.fichier_profil):
                os.remove(self.fichier_profil)
            
            # 💾 CRÉER NOUVEAU PROFIL
            self.sauvegarder_profil()
            
            print("🧽 Profil réinitialisé - Prêt pour nouvel utilisateur")
            return True
            
        except Exception as e:
            print(f"❌ Erreur réinitialisation: {e}")
            return False

    def obtenir_contexte_personnalise(self) -> str:
        """
        Génère le contexte personnalisé selon le profil utilisateur
        """
        nom = self.profil_actuel["nom_utilisateur"]
        niveau = self.profil_actuel["niveau_familiarite"]
        
        # 🎯 CHOISIR LE TEMPLATE SELON LE NIVEAU
        if not self.profil_actuel["personnalisation_active"]:
            template = self.templates_contexte["neutre"]
        elif niveau == "expert":
            template = self.templates_contexte["expert"]
        elif niveau in ["familier", "intime"]:
            template = self.templates_contexte["familier"]
        else:
            template = self.templates_contexte["personnalise"]
        
        # 🔄 REMPLACER LES VARIABLES
        contexte = template.format(nom_utilisateur=nom)
        
        # 🗣️ AJOUTER LE STYLE DE COMMUNICATION
        style = self.styles_communication.get(
            self.profil_actuel["style_communication"], 
            self.styles_communication["naturel"]
        )
        
        contexte += f"\n\nStyle de communication: {style['ton']}"
        
        return contexte

    def adapter_neurone_memoire(self, contenu_neurone: str) -> str:
        """
        Adapte le contenu d'un neurone de mémoire au profil utilisateur
        """
        nom = self.profil_actuel["nom_utilisateur"]
        
        # 🔄 REMPLACEMENTS DYNAMIQUES
        remplacements = {
            "{nom_utilisateur}": nom,
            "{utilisateur}": nom,
            "Jean-Luc Passave": nom,
            "Jean-Luc": nom,
            "Passave": nom
        }
        
        contenu_adapte = contenu_neurone
        for ancien, nouveau in remplacements.items():
            contenu_adapte = contenu_adapte.replace(ancien, nouveau)
        
        return contenu_adapte

    def evoluer_familiarite(self, nombre_interactions: int):
        """
        Fait évoluer le niveau de familiarité selon les interactions
        """
        ancien_niveau = self.profil_actuel["niveau_familiarite"]
        
        if nombre_interactions > 100:
            nouveau_niveau = "expert"
        elif nombre_interactions > 50:
            nouveau_niveau = "intime"
        elif nombre_interactions > 10:
            nouveau_niveau = "familier"
        else:
            nouveau_niveau = "initial"
        
        if nouveau_niveau != ancien_niveau:
            self.profil_actuel["niveau_familiarite"] = nouveau_niveau
            self.sauvegarder_profil()
            print(f"📈 Niveau familiarité évolué: {ancien_niveau} → {nouveau_niveau}")

    def obtenir_salutation_adaptee(self) -> str:
        """
        Retourne une salutation adaptée au profil
        """
        style = self.styles_communication.get(
            self.profil_actuel["style_communication"],
            self.styles_communication["naturel"]
        )
        
        return style["salutation"].format(
            nom_utilisateur=self.profil_actuel["nom_utilisateur"]
        )

    def est_personnalise(self) -> bool:
        """Vérifie si le profil est personnalisé"""
        return self.profil_actuel["personnalisation_active"]

    def obtenir_stats_profil(self) -> Dict[str, Any]:
        """Retourne les statistiques du profil"""
        return {
            "nom_utilisateur": self.profil_actuel["nom_utilisateur"],
            "genre": self.profil_actuel["genre"],
            "niveau_familiarite": self.profil_actuel["niveau_familiarite"],
            "style_communication": self.profil_actuel["style_communication"],
            "personnalisation_active": self.profil_actuel["personnalisation_active"],
            "date_creation": self.profil_actuel["date_creation"],
            "derniere_connexion": self.profil_actuel["derniere_connexion"]
        }

# 🌟 INSTANCE GLOBALE
profil_utilisateur = ProfilUtilisateurDynamique()

def configurer_utilisateur_global(nom: str, prenom: str = None, genre: str = "neutre", 
                                 style: str = "naturel") -> bool:
    """Interface globale pour configurer un utilisateur"""
    return profil_utilisateur.configurer_utilisateur(nom, prenom, genre, style)

def reinitialiser_profil_global() -> bool:
    """Interface globale pour réinitialiser le profil"""
    return profil_utilisateur.reinitialiser_profil()

def obtenir_contexte_personnalise_global() -> str:
    """Interface globale pour obtenir le contexte personnalisé"""
    return profil_utilisateur.obtenir_contexte_personnalise()

def adapter_contenu_utilisateur_global(contenu: str) -> str:
    """Interface globale pour adapter du contenu au profil utilisateur"""
    return profil_utilisateur.adapter_neurone_memoire(contenu)

if __name__ == "__main__":
    print("👤 Test Profil Utilisateur Dynamique")
    
    # Test configuration
    print("\n🔧 Test configuration utilisateur:")
    configurer_utilisateur_global("Alice", "Alice", "féminin", "naturel")
    
    # Test contexte
    contexte = obtenir_contexte_personnalise_global()
    print(f"\nContexte personnalisé:\n{contexte}")
    
    # Test adaptation contenu
    contenu_test = "La conversation avec {nom_utilisateur} est permanente."
    contenu_adapte = adapter_contenu_utilisateur_global(contenu_test)
    print(f"\nAdaptation contenu:")
    print(f"Avant: {contenu_test}")
    print(f"Après: {contenu_adapte}")
    
    # Stats
    stats = profil_utilisateur.obtenir_stats_profil()
    print(f"\nStats profil: {stats}")
    
    # Test réinitialisation
    print(f"\n🧽 Test réinitialisation:")
    reinitialiser_profil_global()
    
    stats_apres = profil_utilisateur.obtenir_stats_profil()
    print(f"Stats après reset: {stats_apres}")
