#!/bin/bash

# 🚀 LANCEUR ELECTRON JARVIS VALIDÉ - JEAN-LUC PASSAVE
# Double-clic pour démarrer l'application validée - CORRIGÉ 25 JUIN 2025

echo "🤖 JARVIS ELECTRON LAUNCHER VALIDÉ - JEAN-LUC PASSAVE"
echo "======================================================"
echo "📅 Version corrigée du 25 juin 2025"
echo ""

# Aller dans le bon répertoire
cd "$(dirname "$0")"
echo "📁 Répertoire de travail: $(pwd)"

# Vérifier les outils système
echo "🔍 Vérification des outils système..."
if ! command -v python3 &> /dev/null; then
    echo "❌ Python3 non trouvé"
    read -p "Appuyez sur Entrée pour fermer..."
    exit 1
fi

if ! command -v node &> /dev/null; then
    echo "❌ Node.js non trouvé"
    read -p "Appuyez sur Entrée pour fermer..."
    exit 1
fi

echo "✅ Python3: $(python3 --version)"
echo "✅ Node.js: $(node --version)"
echo "✅ NPM: $(npm --version)"

# Nettoyer TOUS les anciens processus JARVIS
echo ""
echo "🧹 Nettoyage complet des anciens processus JARVIS..."
pkill -f "jarvis" 2>/dev/null || true
pkill -f "python.*jarvis" 2>/dev/null || true
pkill -f "electron.*jarvis" 2>/dev/null || true
pkill -f "port.*810" 2>/dev/null || true
pkill -f "gradio" 2>/dev/null || true
sleep 5
echo "✅ Nettoyage terminé"

# Vérifier les fichiers requis
echo ""
echo "🔍 Vérification des fichiers requis..."

if [ -f "jarvis_electron_nouveau.js" ]; then
    echo "✅ Application Electron validée trouvée"
else
    echo "❌ Application Electron manquante: jarvis_electron_nouveau.js"
    echo "📁 Fichiers disponibles:"
    ls -la *.js 2>/dev/null || echo "Aucun fichier .js trouvé"
    read -p "Appuyez sur Entrée pour fermer..."
    exit 1
fi

if [ -f "jarvis_interface_validee_finale.py" ]; then
    echo "✅ Interface JARVIS VALIDÉE trouvée"
else
    echo "❌ Interface VALIDÉE manquante: jarvis_interface_validee_finale.py"
    echo "📁 Fichiers Python disponibles:"
    ls -la jarvis*.py 2>/dev/null || echo "Aucun fichier jarvis*.py trouvé"
    read -p "Appuyez sur Entrée pour fermer..."
    exit 1
fi

if [ -f "package.json" ]; then
    echo "✅ Configuration NPM trouvée"
else
    echo "❌ package.json manquant"
    read -p "Appuyez sur Entrée pour fermer..."
    exit 1
fi

# Vérifier et installer Electron si nécessaire
echo ""
echo "🔍 Vérification d'Electron..."
if [ -f "node_modules/.bin/electron" ]; then
    echo "✅ Electron installé"
else
    echo "🔧 Installation d'Electron en cours..."
    npm install
    if [ $? -eq 0 ]; then
        echo "✅ Electron installé avec succès"
    else
        echo "❌ Erreur lors de l'installation d'Electron"
        read -p "Appuyez sur Entrée pour fermer..."
        exit 1
    fi
fi

echo ""
echo "🚀 DÉMARRAGE JARVIS COMPLET..."
echo "🧠 Interface validée Jean-Luc Passave"
echo "📊 QI 648 + 100M neurones actifs"
echo ""

# Démarrer l'interface JARVIS MULTI-FENÊTRES
echo "⚡ Démarrage de l'interface Python JARVIS MULTI-FENÊTRES..."
python3 jarvis_architecture_multi_fenetres.py &
JARVIS_PID=$!
echo "✅ JARVIS MULTI-FENÊTRES démarré (PID: $JARVIS_PID)"

# Attendre que l'interface soit prête avec vérification progressive
echo "⏳ Attente du démarrage complet de l'interface..."
for i in {1..15}; do
    echo "⏳ Vérification $i/15..."
    if curl -s http://localhost:7867 > /dev/null 2>&1; then
        echo "✅ Interface JARVIS MULTI-FENÊTRES accessible sur port 7867"
        break
    elif curl -s http://localhost:8101 > /dev/null 2>&1; then
        echo "✅ Interface JARVIS DASHBOARD accessible sur port 8101"
        break
    else
        sleep 2
    fi
done

# Vérification finale des ports
echo ""
echo "🔍 Vérification finale des ports JARVIS..."
if curl -s http://localhost:7867 > /dev/null 2>&1; then
    echo "✅ Port 7867: Interface JARVIS MULTI-FENÊTRES active"
    JARVIS_PORT=7867
elif curl -s http://localhost:8101 > /dev/null 2>&1; then
    echo "✅ Port 8101: Interface JARVIS DASHBOARD active"
    JARVIS_PORT=8101
else
    echo "⚠️ Aucun port JARVIS détecté, tentative de démarrage Electron..."
    JARVIS_PORT=7867
fi

echo ""
echo "🖥️ DÉMARRAGE APPLICATION ELECTRON VALIDÉE..."
echo "🎯 Interface Dashboard violette validée"
echo "🧠 QI 648 + 100M neurones actifs"
echo "🎨 BOOST IMAGINATION + Rafraîchissement automatique"
echo "🔄 Pensées temps réel + Cycle cognitif continu"
echo "🌙 Rêves créatifs + Gestion énergie"
echo "🔧 Analyse code + 20+ interfaces spécialisées"
echo "🌐 Interface JARVIS: http://localhost:$JARVIS_PORT"
echo ""

# Démarrer l'application Electron validée
echo "🚀 Lancement de l'application Electron..."
if npm start; then
    echo "✅ Application Electron démarrée avec succès"
else
    echo "❌ Erreur lors du démarrage d'Electron"
    echo "🔧 Tentative de démarrage direct..."
    ./node_modules/.bin/electron . || echo "❌ Échec du démarrage direct"
fi

echo ""
echo "🔄 Application fermée"
echo "🛑 Arrêt de JARVIS..."

# Arrêter proprement tous les processus JARVIS
if [ ! -z "$JARVIS_PID" ]; then
    kill $JARVIS_PID 2>/dev/null || true
fi
pkill -f "jarvis" 2>/dev/null || true
pkill -f "python.*jarvis" 2>/dev/null || true

echo "✅ JARVIS arrêté proprement"
echo ""
echo "👋 Merci d'avoir utilisé JARVIS, Jean-Luc !"
read -p "Appuyez sur Entrée pour fermer cette fenêtre..."
