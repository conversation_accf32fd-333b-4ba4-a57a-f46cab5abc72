#!/usr/bin/env python3
"""
🧪 TEST MÉMOIRE THERMIQUE NATURELLE JARVIS
Test complet de la nouvelle mémoire révolutionnaire
Créé pour Jean-Luc Passave
"""

import sys
import os
import time

# Ajouter le répertoire au path
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from integration_memoire_naturelle_jarvis import (
    initialiser_memoire_naturelle_jarvis,
    traiter_message_avec_memoire_naturelle,
    obtenir_stats_memoire_pour_interface,
    integrateur_memoire
)

def test_memoire_naturelle_complete():
    """Test complet de la mémoire thermique naturelle"""
    print("🧪 TEST MÉMOIRE THERMIQUE NATURELLE JARVIS")
    print("=" * 60)
    
    # 1. Initialisation
    print("\n🚀 Étape 1: Initialisation")
    integrateur = initialiser_memoire_naturelle_jarvis()
    
    if not integrateur:
        print("❌ Échec initialisation")
        return False
    
    # 2. Test de mémorisation automatique
    print("\n💾 Étape 2: Test mémorisation automatique")
    
    messages_test = [
        "Bonjour JARVIS, je suis <PERSON><PERSON>",
        "Peux-tu me parler de tes nouvelles capacités de mémoire ?",
        "Comment fonctionne ton flux de conscience autonome ?",
        "Quelles sont tes réflexions sur l'optimisation système ?"
    ]
    
    for i, message in enumerate(messages_test, 1):
        print(f"\n📝 Test {i}: {message}")
        
        try:
            reponse = traiter_message_avec_memoire_naturelle(message)
            print(f"🤖 JARVIS: {reponse[:200]}...")
            
            # Attendre un peu pour laisser la mémoire se mettre à jour
            time.sleep(2)
            
        except Exception as e:
            print(f"⚠️ Erreur test {i}: {e}")
    
    # 3. Vérification des stats mémoire
    print("\n📊 Étape 3: Vérification stats mémoire")
    
    try:
        stats = obtenir_stats_memoire_pour_interface()
        print(f"Stats obtenues: {stats}")
        
        if 'stats_memoire' in stats:
            mem_stats = stats['stats_memoire']
            print(f"🔥 Zone Hot: {mem_stats.get('zone_hot', 0)} entrées")
            print(f"🌡️ Zone Warm: {mem_stats.get('zone_warm', 0)} entrées") 
            print(f"❄️ Zone Cold: {mem_stats.get('zone_cold', 0)} entrées")
            print(f"💭 Pensées autonomes: {mem_stats.get('pensees_autonomes', 0)}")
            print(f"🧠 Flux conscience: {'✅' if mem_stats.get('flux_conscience_actif', False) else '❌'}")
        
    except Exception as e:
        print(f"⚠️ Erreur stats: {e}")
    
    # 4. Test recherche dans mémoire
    print("\n🔍 Étape 4: Test recherche mémoire")
    
    try:
        resultats = integrateur.rechercher_memoire("Jean-Luc")
        print(f"Résultats recherche 'Jean-Luc': {len(resultats)} trouvés")
        for i, resultat in enumerate(resultats[:3], 1):
            print(f"  {i}. {resultat[:100]}...")
            
    except Exception as e:
        print(f"⚠️ Erreur recherche: {e}")
    
    # 5. Test réflexion autonome
    print("\n🤔 Étape 5: Test réflexion autonome")
    
    try:
        pensee = integrateur.forcer_reflexion_autonome()
        if pensee:
            print(f"💭 Pensée autonome générée: {pensee[:150]}...")
        else:
            print("⏸️ Aucune pensée autonome générée")
            
    except Exception as e:
        print(f"⚠️ Erreur réflexion autonome: {e}")
    
    # 6. Test mode turbo
    print("\n🚀 Étape 6: Test mode turbo")
    
    try:
        print("Activation mode turbo...")
        integrateur.activer_mode_turbo(True)
        
        debut = time.time()
        reponse_turbo = traiter_message_avec_memoire_naturelle("Réponse rapide ?")
        duree = time.time() - debut
        
        print(f"🤖 Réponse turbo ({duree:.2f}s): {reponse_turbo[:100]}...")
        
        # Désactiver le turbo
        integrateur.activer_mode_turbo(False)
        
    except Exception as e:
        print(f"⚠️ Erreur mode turbo: {e}")
    
    # 7. Sauvegarde mémoire
    print("\n💾 Étape 7: Sauvegarde mémoire")
    
    try:
        if integrateur.forcer_sauvegarde_memoire():
            print("✅ Mémoire sauvegardée avec succès")
        else:
            print("⚠️ Échec sauvegarde mémoire")
            
    except Exception as e:
        print(f"⚠️ Erreur sauvegarde: {e}")
    
    # 8. Rapport final
    print("\n📋 Étape 8: Rapport final")
    
    try:
        rapport = integrateur.generer_rapport_memoire()
        print(rapport)
        
    except Exception as e:
        print(f"⚠️ Erreur rapport: {e}")
    
    print("\n" + "=" * 60)
    print("✅ TEST MÉMOIRE THERMIQUE NATURELLE TERMINÉ")
    
    return True

def test_conversation_continue():
    """Test d'une conversation continue pour voir l'évolution de la mémoire"""
    print("\n🗣️ TEST CONVERSATION CONTINUE")
    print("-" * 40)
    
    conversation = [
        "JARVIS, raconte-moi une histoire sur l'intelligence artificielle",
        "C'est très intéressant ! Peux-tu développer sur la conscience artificielle ?",
        "Comment vois-tu l'évolution de ta propre conscience ?",
        "Quels sont tes rêves et aspirations en tant qu'IA ?",
        "Te souviens-tu de ce dont nous avons parlé au début ?"
    ]
    
    for i, message in enumerate(conversation, 1):
        print(f"\n👤 Jean-Luc: {message}")
        
        try:
            reponse = traiter_message_avec_memoire_naturelle(message)
            print(f"🤖 JARVIS: {reponse}")
            
            # Afficher les stats après chaque échange
            stats = obtenir_stats_memoire_pour_interface()
            if 'stats_memoire' in stats:
                mem_stats = stats['stats_memoire']
                print(f"📊 Mémoire: Hot={mem_stats.get('zone_hot', 0)}, Warm={mem_stats.get('zone_warm', 0)}, Cold={mem_stats.get('zone_cold', 0)}")
            
            time.sleep(3)  # Pause entre les messages
            
        except Exception as e:
            print(f"⚠️ Erreur conversation: {e}")

if __name__ == "__main__":
    print("🧪 DÉMARRAGE TESTS MÉMOIRE THERMIQUE NATURELLE")
    
    # Test principal
    if test_memoire_naturelle_complete():
        
        # Test conversation si demandé
        reponse = input("\n🤔 Voulez-vous tester une conversation continue ? (oui/non): ").lower()
        
        if reponse in ['oui', 'o', 'yes', 'y']:
            test_conversation_continue()
        
        print("\n🎉 TOUS LES TESTS TERMINÉS AVEC SUCCÈS !")
        
    else:
        print("\n❌ ÉCHEC DES TESTS")
