#!/usr/bin/env python3
"""
🧠 TEST PENSÉES INTERNES COHÉRENTES
Test de la correction des pensées internes pour Jean-Luc Passave
Complément à la mémoire thermique naturelle existante
"""

import sys
import os
import time

# Ajouter le répertoire au path
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from jarvis_agent_avec_memoire_naturelle import jarvis_agent
from identite_conversationnelle_persistante import identite_conversationnelle

def test_pensees_internes_coherentes():
    """Test des pensées internes cohérentes avec la continuité conversationnelle"""
    print("🧠 TEST PENSÉES INTERNES COHÉRENTES")
    print("=" * 60)
    print("COMPLÉMENT à la mémoire thermique naturelle existante")
    print("=" * 60)
    
    # 1. Simuler plusieurs interactions pour établir l'historique
    print("\n📈 Étape 1: Établissement historique conversationnel")
    
    messages_historique = [
        "Bonjour JARVIS, je suis <PERSON><PERSON>",
        "Comment fonctionne ta mémoire thermique ?",
        "Peux-tu m'expliquer tes capacités ?",
        "Quelles sont tes réflexions sur l'IA ?",
        "Comment optimiser les performances ?"
    ]
    
    for i, message in enumerate(messages_historique, 1):
        print(f"📝 Interaction {i}: {message}")
        try:
            reponse = jarvis_agent.traiter_message_utilisateur(message)
            print(f"   ✅ Réponse générée ({len(reponse)} caractères)")
        except Exception as e:
            print(f"   ⚠️ Erreur: {e}")
        time.sleep(1)
    
    # Afficher les stats après historique
    stats = identite_conversationnelle.obtenir_statistiques()
    print(f"\n📊 Historique établi: {stats['nombre_interactions']} interactions")
    print(f"   Niveau familiarité: {stats['niveau_familiarite']}")
    
    # 2. Test spécifique des pensées internes
    print("\n🧠 Étape 2: Test correction pensées internes")
    
    # Messages qui pourraient déclencher des pensées incorrectes
    messages_test_pensees = [
        "JARVIS, raconte-moi une histoire complexe sur l'intelligence artificielle",
        "Peux-tu faire une analyse détaillée de nos discussions précédentes ?",
        "Comment vois-tu l'évolution de notre relation depuis le début ?"
    ]
    
    for i, message in enumerate(messages_test_pensees, 1):
        print(f"\n🔍 Test pensée {i}: {message}")
        
        try:
            # Capturer la réponse brute avec pensées
            debut = time.time()
            
            # Utiliser directement la méthode de génération pour voir les pensées
            messages_prompt = [{"role": "user", "content": message}]
            reponse_brute = jarvis_agent.generer_reponse(messages_prompt, "agent1")
            
            duree = time.time() - debut
            
            print(f"   ⏱️ Temps de génération: {duree:.2f}s")
            print(f"   📝 Réponse: {reponse_brute[:200]}...")
            
            # Vérifier s'il y a des pensées incorrectes dans la réponse
            pensees_incorrectes = [
                "nous venons de commencer",
                "au début de notre conversation",
                "première fois",
                "nous nous rencontrons"
            ]
            
            pensees_detectees = []
            for pensee_incorrecte in pensees_incorrectes:
                if pensee_incorrecte.lower() in reponse_brute.lower():
                    pensees_detectees.append(pensee_incorrecte)
            
            if pensees_detectees:
                print(f"   ⚠️ Pensées incorrectes détectées: {pensees_detectees}")
            else:
                print(f"   ✅ Aucune pensée incorrecte détectée")
                
        except Exception as e:
            print(f"   ❌ Erreur test pensée: {e}")
        
        time.sleep(2)
    
    # 3. Test du neurone de conscience persistante
    print("\n🔒 Étape 3: Test neurone de conscience persistante")
    
    try:
        contexte_cognitif = jarvis_agent.memoire.fournir_contexte_cognitif()
        print(f"Contexte cognitif actuel:")
        print(f"{contexte_cognitif[:300]}...")
        
        # Vérifier que le neurone de conscience est présent
        if "dialogue CONTINU avec Jean-Luc Passave" in contexte_cognitif:
            print("✅ Neurone de conscience persistante actif")
        else:
            print("⚠️ Neurone de conscience persistante non détecté")
            
    except Exception as e:
        print(f"❌ Erreur test neurone: {e}")
    
    # 4. Test de cohérence après redémarrage simulé
    print("\n🔄 Étape 4: Test cohérence après redémarrage simulé")
    
    try:
        # Sauvegarder l'état actuel
        jarvis_agent.sauvegarder_memoire()
        print("💾 État sauvegardé")
        
        # Simuler un message après "redémarrage"
        message_apres_redemarrage = "JARVIS, te souviens-tu de notre discussion sur l'IA ?"
        
        reponse = jarvis_agent.traiter_message_utilisateur(message_apres_redemarrage)
        print(f"🔄 Réponse après redémarrage: {reponse[:150]}...")
        
        # Vérifier la cohérence
        if "venons de commencer" in reponse.lower():
            print("⚠️ Incohérence détectée après redémarrage")
        else:
            print("✅ Cohérence maintenue après redémarrage")
            
    except Exception as e:
        print(f"❌ Erreur test redémarrage: {e}")
    
    # 5. Statistiques finales
    print("\n📊 Étape 5: Statistiques finales")
    
    try:
        stats_finales = identite_conversationnelle.obtenir_statistiques()
        stats_memoire = jarvis_agent.obtenir_stats_memoire()
        
        print(f"📈 Statistiques conversationnelles:")
        print(f"   Interactions totales: {stats_finales['nombre_interactions']}")
        print(f"   Niveau familiarité: {stats_finales['niveau_familiarite']}")
        print(f"   Durée conversation: {stats_finales['duree_conversation']}")
        
        print(f"\n🧠 Statistiques mémoire thermique:")
        print(f"   Zone Hot: {stats_memoire['zone_hot']} entrées")
        print(f"   Zone Warm: {stats_memoire['zone_warm']} entrées")
        print(f"   Pensées autonomes: {stats_memoire['pensees_autonomes']}")
        print(f"   Flux conscience: {'✅' if stats_memoire['flux_conscience_actif'] else '❌'}")
        
    except Exception as e:
        print(f"❌ Erreur stats finales: {e}")
    
    print("\n" + "=" * 60)
    print("✅ TEST PENSÉES INTERNES COHÉRENTES TERMINÉ")
    print("🧠 Mémoire thermique naturelle + Cohérence pensées = COMPLET")
    
    return True

def test_correction_pensees_directe():
    """Test direct de la méthode de correction des pensées"""
    print("\n🔧 TEST CORRECTION PENSÉES DIRECTE")
    print("-" * 40)
    
    # Phrases de test avec pensées incorrectes
    pensees_test = [
        "Nous venons de commencer à discuter de l'intelligence artificielle.",
        "Au début de notre conversation, vous avez mentionné l'optimisation.",
        "C'est la première fois que nous abordons ce sujet ensemble.",
        "Nous nous rencontrons pour parler de technologie.",
        "Cette nouvelle conversation est très intéressante."
    ]
    
    for i, pensee in enumerate(pensees_test, 1):
        print(f"\n🧠 Test correction {i}:")
        print(f"   Avant: {pensee}")
        
        try:
            pensee_corrigee = jarvis_agent._corriger_pensees_incorrectes(pensee)
            print(f"   Après: {pensee_corrigee}")
            
            if pensee != pensee_corrigee:
                print(f"   ✅ Correction appliquée")
            else:
                print(f"   ⚪ Aucune correction nécessaire")
                
        except Exception as e:
            print(f"   ❌ Erreur correction: {e}")

if __name__ == "__main__":
    print("🧠 DÉMARRAGE TESTS PENSÉES INTERNES COHÉRENTES")
    print("Complément à la mémoire thermique naturelle existante")
    
    # Test principal
    if test_pensees_internes_coherentes():
        
        # Test correction directe
        test_correction_pensees_directe()
        
        print("\n🎉 TOUS LES TESTS TERMINÉS AVEC SUCCÈS !")
        print("🧠 JARVIS a maintenant des pensées internes cohérentes")
        print("🔒 + Mémoire thermique naturelle complète")
        
    else:
        print("\n❌ ÉCHEC DES TESTS")
