const { app, BrowserWindow, ipcMain } = require('electron');
const path = require('path');
const { spawn } = require('child_process');
const os = require('os');

let mainWindow;
let jarvisProcess = null;

// Informations système
const systemInfo = {
    arch: os.arch(),
    platform: os.platform(),
    cpus: os.cpus().length,
    totalMemory: Math.round(os.totalmem() / 1024 / 1024 / 1024)
};

console.log('🍎 NOUVEAU JARVIS ELECTRON');
console.log('=====================================');
console.log('🔧 Architecture:', systemInfo.arch);
console.log('🍎 Apple Silicon:', systemInfo.arch === 'arm64' ? 'OUI' : 'NON');
console.log('💾 RAM Totale:', systemInfo.totalMemory, 'GB');
console.log('⚡ Cœurs CPU:', systemInfo.cpus);

function createWindow() {
    console.log('🖥️ CRÉATION FENÊTRE PRINCIPALE...');

    mainWindow = new BrowserWindow({
        width: 1400,
        height: 900,
        webPreferences: {
            nodeIntegration: true,
            contextIsolation: false,
            webSecurity: false,
            // Optimisations pour éviter les crashes GPU
            enableRemoteModule: false,
            backgroundThrottling: false,
            offscreen: false
        },
        icon: path.join(__dirname, 'assets', 'jarvis-icon.png'),
        titleBarStyle: 'default',  // Barre de titre normale pour pouvoir déplacer
        show: false,
        movable: true,  // Fenêtre déplaçable
        resizable: true,  // Fenêtre redimensionnable
        minimizable: true,  // Fenêtre minimisable
        maximizable: true,  // Fenêtre maximisable
        closable: true,  // Fenêtre fermable
        frame: true  // Afficher la barre de titre complète
    });

    // Page de chargement temporaire
    mainWindow.loadURL('data:text/html;charset=utf-8,' + encodeURIComponent(`
        <!DOCTYPE html>
        <html>
        <head>
            <title>🤖 JARVIS - Chargement...</title>
            <style>
                body {
                    font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
                    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
                    color: white;
                    display: flex;
                    justify-content: center;
                    align-items: center;
                    height: 100vh;
                    margin: 0;
                    text-align: center;
                }
                .loading {
                    font-size: 2em;
                    animation: pulse 2s infinite;
                }
                @keyframes pulse {
                    0% { opacity: 0.5; }
                    50% { opacity: 1; }
                    100% { opacity: 0.5; }
                }
            </style>
        </head>
        <body>
            <div class="loading">
                <h1>🤖 JARVIS</h1>
                <p>Démarrage en cours...</p>
                <p>⚡ Chargement de votre interface personnalisée</p>
            </div>
        </body>
        </html>
    `));

    // Fonction pour essayer de charger JARVIS
    function tryLoadJarvis() {
        console.log('🌐 Tentative de chargement JARVIS Dashboard...');

        // Test de connexion - CORRIGÉ JEAN-LUC PASSAVE
        const { net } = require('electron');
        const request = net.request('http://localhost:7867');

        request.on('response', (response) => {
            if (response.statusCode === 200) {
                console.log('✅ JARVIS Dashboard accessible, chargement...');
                mainWindow.loadURL('http://localhost:7867');
            } else {
                console.log('⏳ JARVIS pas encore prêt, nouvelle tentative dans 2s...');
                setTimeout(tryLoadJarvis, 2000);
            }
        });

        request.on('error', (error) => {
            console.log('⏳ JARVIS pas encore prêt, nouvelle tentative dans 2s...');
            setTimeout(tryLoadJarvis, 2000);
        });

        request.end();
    }

    // Commencer les tentatives après 5 secondes
    setTimeout(tryLoadJarvis, 5000);

    // Fenêtre prête à afficher
    mainWindow.once('ready-to-show', () => {
        console.log('✅ Fenêtre prête à afficher');
        mainWindow.show();
    });
}

// Fonction pour démarrer JARVIS automatiquement
function startJarvis() {
    console.log('🚀 Démarrage automatique JARVIS...');

    const jarvisCommand = 'python3 jarvis_architecture_multi_fenetres.py';

    jarvisProcess = spawn('python3', ['jarvis_architecture_multi_fenetres.py'], {
        cwd: __dirname,
        stdio: ['pipe', 'pipe', 'pipe'],
        // Optimisations pour éviter les crashes
        env: {
            ...process.env,
            PYTHONUNBUFFERED: '1',
            ELECTRON_DISABLE_SECURITY_WARNINGS: 'true',
            PYTHONDONTWRITEBYTECODE: '1'
        }
    });

    // Capturer la sortie pour debug
    jarvisProcess.stdout.on('data', (data) => {
        const output = data.toString();
        if (output.includes('ERROR') || output.includes('❌')) {
            console.log('🔴 JARVIS Error:', output.trim());
        } else if (output.includes('✅') || output.includes('🚀')) {
            console.log('✅ JARVIS:', output.trim());
        }
    });

    jarvisProcess.stderr.on('data', (data) => {
        console.log('⚠️ JARVIS stderr:', data.toString().trim());
    });

    // Gestion des erreurs du processus Python
    jarvisProcess.on('error', (error) => {
        console.log('❌ Erreur processus JARVIS:', error.message);
    });

    jarvisProcess.on('exit', (code, signal) => {
        console.log(`🔴 JARVIS terminé - Code: ${code}, Signal: ${signal}`);
        if (code !== 0 && code !== null && code !== 15) {
            console.log('⚠️ Redémarrage automatique de JARVIS dans 10 secondes...');
            setTimeout(() => {
                startJarvis();
            }, 10000);
        }
    });

    if (jarvisProcess.pid) {
        console.log('✅ JARVIS démarré automatiquement (PID:', jarvisProcess.pid + ')');
    } else {
        console.log('❌ Échec démarrage JARVIS');
    }
}

app.whenReady().then(() => {
    // Démarrer JARVIS en premier
    startJarvis();
    // Puis créer la fenêtre
    createWindow();
});

app.on('window-all-closed', () => {
    console.log('🔴 Fermeture de toutes les fenêtres');

    // Arrêter JARVIS si il est en cours d'exécution
    if (jarvisProcess) {
        console.log('🛑 Arrêt de JARVIS...');
        jarvisProcess.kill();
        jarvisProcess = null;
    }

    if (process.platform !== 'darwin') {
        app.quit();
    }
});

app.on('activate', () => {
    if (BrowserWindow.getAllWindows().length === 0) {
        createWindow();
    }
});

console.log('✅ NOUVEAU JARVIS ELECTRON LANCÉ');


