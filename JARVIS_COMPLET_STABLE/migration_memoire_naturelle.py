#!/usr/bin/env python3
"""
🔄 MIGRATION MÉMOIRE THERMIQUE NATURELLE POUR JARVIS COMPLET
Script de migration pour Jean-Luc Passave
Intègre la mémoire naturelle dans l'architecture multi-fenêtres existante
"""

import os
import sys
import shutil
from datetime import datetime

# Ajouter le répertoire parent au path pour les imports
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from integration_memoire_naturelle_jarvis import (
    initialiser_memoire_naturelle_jarvis,
    traiter_message_avec_memoire_naturelle,
    obtenir_stats_memoire_pour_interface,
    activer_turbo_jarvis,
    gerer_conscience_jarvis
)

class MigrationMemoireNaturelle:
    """
    GESTIONNAIRE DE MIGRATION POUR INTÉGRER LA MÉMOIRE NATURELLE
    """
    
    def __init__(self):
        self.chemin_jarvis_complet = os.path.dirname(os.path.abspath(__file__))
        self.fichier_architecture = os.path.join(self.chemin_jarvis_complet, "jarvis_architecture_multi_fenetres.py")
        self.backup_dir = os.path.join(self.chemin_jarvis_complet, f"backup_migration_{datetime.now().strftime('%Y%m%d_%H%M%S')}")
        
        print(f"🔄 Migration Mémoire Naturelle initialisée")
        print(f"📁 Répertoire JARVIS: {self.chemin_jarvis_complet}")

    def creer_backup_securite(self):
        """Crée un backup de sécurité avant migration"""
        try:
            os.makedirs(self.backup_dir, exist_ok=True)
            
            # Backup du fichier principal
            if os.path.exists(self.fichier_architecture):
                shutil.copy2(self.fichier_architecture, self.backup_dir)
                print(f"✅ Backup créé: {self.backup_dir}")
                return True
            else:
                print(f"⚠️ Fichier architecture non trouvé: {self.fichier_architecture}")
                return False
                
        except Exception as e:
            print(f"❌ Erreur création backup: {e}")
            return False

    def copier_fichiers_memoire_naturelle(self):
        """Copie les nouveaux fichiers de mémoire naturelle"""
        try:
            fichiers_a_copier = [
                "jarvis_memoire_thermique_integree_naturelle.py",
                "jarvis_agent_avec_memoire_naturelle.py", 
                "integration_memoire_naturelle_jarvis.py"
            ]
            
            repertoire_parent = os.path.dirname(self.chemin_jarvis_complet)
            
            for fichier in fichiers_a_copier:
                source = os.path.join(repertoire_parent, fichier)
                destination = os.path.join(self.chemin_jarvis_complet, fichier)
                
                if os.path.exists(source):
                    shutil.copy2(source, destination)
                    print(f"📁 Copié: {fichier}")
                else:
                    print(f"⚠️ Fichier source non trouvé: {source}")
            
            return True
            
        except Exception as e:
            print(f"❌ Erreur copie fichiers: {e}")
            return False

    def integrer_dans_architecture_existante(self):
        """Intègre la mémoire naturelle dans l'architecture existante"""
        try:
            # Lire le fichier architecture existant
            with open(self.fichier_architecture, 'r', encoding='utf-8') as f:
                contenu_existant = f.read()
            
            # Vérifier si déjà intégré
            if "integration_memoire_naturelle_jarvis" in contenu_existant:
                print("✅ Mémoire naturelle déjà intégrée")
                return True
            
            # Ajouter les imports en haut du fichier
            imports_memoire_naturelle = '''
# 🧠 IMPORTS MÉMOIRE THERMIQUE NATURELLE - JEAN-LUC PASSAVE
try:
    from integration_memoire_naturelle_jarvis import (
        initialiser_memoire_naturelle_jarvis,
        traiter_message_avec_memoire_naturelle,
        obtenir_stats_memoire_pour_interface,
        activer_turbo_jarvis,
        gerer_conscience_jarvis,
        integrateur_memoire
    )
    MEMOIRE_NATURELLE_DISPONIBLE = True
    print("🧠 Mémoire Thermique Naturelle chargée avec succès")
except ImportError as e:
    MEMOIRE_NATURELLE_DISPONIBLE = False
    print(f"⚠️ Mémoire Naturelle non disponible: {e}")
'''
            
            # Insérer après les imports existants
            lignes = contenu_existant.split('\n')
            position_insertion = 0
            
            # Trouver la fin des imports
            for i, ligne in enumerate(lignes):
                if ligne.startswith('import ') or ligne.startswith('from '):
                    position_insertion = i + 1
            
            # Insérer les nouveaux imports
            lignes.insert(position_insertion, imports_memoire_naturelle)
            
            # Ajouter fonction de traitement avec mémoire naturelle
            fonction_traitement = '''

def traiter_message_avec_memoire_naturelle_jarvis(message, agent_type="agent1"):
    """
    FONCTION DE TRAITEMENT AVEC MÉMOIRE NATURELLE INTÉGRÉE
    Remplace le traitement standard par la mémoire thermique naturelle
    """
    if MEMOIRE_NATURELLE_DISPONIBLE:
        try:
            return traiter_message_avec_memoire_naturelle(message, agent_type)
        except Exception as e:
            print(f"⚠️ Erreur mémoire naturelle, fallback standard: {e}")
            # Fallback vers traitement standard si erreur
            return traiter_message_standard_jarvis(message, agent_type)
    else:
        return traiter_message_standard_jarvis(message, agent_type)

def obtenir_contexte_memoire_naturelle_jarvis():
    """Obtient le contexte de la mémoire naturelle pour l'interface"""
    if MEMOIRE_NATURELLE_DISPONIBLE:
        try:
            return obtenir_stats_memoire_pour_interface()
        except Exception as e:
            print(f"⚠️ Erreur contexte mémoire naturelle: {e}")
            return {}
    return {}

def initialiser_memoire_naturelle_au_demarrage():
    """Initialise la mémoire naturelle au démarrage de JARVIS"""
    if MEMOIRE_NATURELLE_DISPONIBLE:
        try:
            integrateur = initialiser_memoire_naturelle_jarvis()
            if integrateur:
                print("🧠 Mémoire Thermique Naturelle initialisée avec succès")
                return True
        except Exception as e:
            print(f"⚠️ Erreur initialisation mémoire naturelle: {e}")
    return False
'''
            
            # Ajouter à la fin du fichier
            lignes.append(fonction_traitement)
            
            # Réécrire le fichier
            contenu_modifie = '\n'.join(lignes)
            
            with open(self.fichier_architecture, 'w', encoding='utf-8') as f:
                f.write(contenu_modifie)
            
            print("✅ Architecture modifiée avec succès")
            return True
            
        except Exception as e:
            print(f"❌ Erreur modification architecture: {e}")
            return False

    def creer_script_demarrage_memoire_naturelle(self):
        """Crée un script de démarrage spécifique pour la mémoire naturelle"""
        script_demarrage = f'''#!/usr/bin/env python3
"""
🚀 DÉMARRAGE JARVIS AVEC MÉMOIRE THERMIQUE NATURELLE
Script de démarrage pour Jean-Luc Passave
"""

import os
import sys

# Ajouter le répertoire au path
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

def demarrer_jarvis_memoire_naturelle():
    """Démarre JARVIS avec mémoire thermique naturelle"""
    print("🚀 Démarrage JARVIS avec Mémoire Thermique Naturelle")
    
    try:
        # Importer et initialiser
        from integration_memoire_naturelle_jarvis import initialiser_memoire_naturelle_jarvis
        
        integrateur = initialiser_memoire_naturelle_jarvis()
        
        if integrateur:
            print("✅ JARVIS avec Mémoire Naturelle prêt")
            
            # Démarrer l'interface principale
            from jarvis_architecture_multi_fenetres import main
            main()
        else:
            print("❌ Échec initialisation mémoire naturelle")
            
    except Exception as e:
        print(f"❌ Erreur démarrage: {{e}}")

if __name__ == "__main__":
    demarrer_jarvis_memoire_naturelle()
'''
        
        try:
            fichier_demarrage = os.path.join(self.chemin_jarvis_complet, "DEMARRER_JARVIS_MEMOIRE_NATURELLE.py")
            
            with open(fichier_demarrage, 'w', encoding='utf-8') as f:
                f.write(script_demarrage)
            
            # Rendre exécutable
            os.chmod(fichier_demarrage, 0o755)
            
            print(f"✅ Script de démarrage créé: {fichier_demarrage}")
            return True
            
        except Exception as e:
            print(f"❌ Erreur création script démarrage: {e}")
            return False

    def executer_migration_complete(self):
        """Exécute la migration complète"""
        print("🚀 DÉBUT MIGRATION MÉMOIRE THERMIQUE NATURELLE")
        print("=" * 60)
        
        # Étape 1: Backup de sécurité
        print("\n📦 Étape 1: Création backup de sécurité")
        if not self.creer_backup_securite():
            print("❌ Échec backup - Migration annulée")
            return False
        
        # Étape 2: Copie des nouveaux fichiers
        print("\n📁 Étape 2: Copie des fichiers mémoire naturelle")
        if not self.copier_fichiers_memoire_naturelle():
            print("❌ Échec copie fichiers - Migration annulée")
            return False
        
        # Étape 3: Intégration dans l'architecture
        print("\n🔧 Étape 3: Intégration dans l'architecture existante")
        if not self.integrer_dans_architecture_existante():
            print("❌ Échec intégration - Migration annulée")
            return False
        
        # Étape 4: Création script de démarrage
        print("\n🚀 Étape 4: Création script de démarrage")
        if not self.creer_script_demarrage_memoire_naturelle():
            print("⚠️ Échec création script démarrage - Migration continue")
        
        print("\n" + "=" * 60)
        print("✅ MIGRATION MÉMOIRE THERMIQUE NATURELLE TERMINÉE")
        print(f"📁 Backup disponible: {self.backup_dir}")
        print("🚀 Utilisez DEMARRER_JARVIS_MEMOIRE_NATURELLE.py pour démarrer")
        
        return True

def main():
    """Fonction principale de migration"""
    print("🧠 MIGRATION MÉMOIRE THERMIQUE NATURELLE JARVIS")
    print("Développé par Jean-Luc Passave")
    print("=" * 60)
    
    migration = MigrationMemoireNaturelle()
    
    # Demander confirmation
    reponse = input("🤔 Voulez-vous procéder à la migration ? (oui/non): ").lower()
    
    if reponse in ['oui', 'o', 'yes', 'y']:
        if migration.executer_migration_complete():
            print("\n🎉 Migration réussie ! JARVIS dispose maintenant de la mémoire thermique naturelle.")
        else:
            print("\n❌ Migration échouée. Vérifiez les logs ci-dessus.")
    else:
        print("🚫 Migration annulée par l'utilisateur")

if __name__ == "__main__":
    main()
