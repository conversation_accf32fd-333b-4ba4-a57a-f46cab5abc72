{"memory_structure": {"max_entries_per_day": 1000, "compression_threshold": 500, "auto_cleanup_days": 30, "priority_levels": ["urgent", "high", "normal", "low", "archive"]}, "learning_parameters": {"habit_detection_threshold": 3, "topic_relevance_score": 0.7, "proactive_suggestion_frequency": 24, "context_window_size": 10}, "user_interaction": {"notification_types": ["audio", "visual", "text"], "proactive_mode": true, "learning_mode": true, "personality_adaptation": true}}