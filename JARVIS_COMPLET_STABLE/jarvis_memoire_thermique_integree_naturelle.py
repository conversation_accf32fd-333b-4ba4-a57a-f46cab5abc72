#!/usr/bin/env python3
"""
🧠 JARVIS MÉMOIRE THERMIQUE INTÉGRÉE NATURELLE
Implémentation révolutionnaire de Jean-Luc Passave
La mémoire thermique devient une extension naturelle du cerveau JARVIS
"""

import json
import time
import random
from datetime import datetime
from collections import deque
from typing import Any, List, Dict, Optional
import threading

class MemoireThermiqueNaturelle:
    """
    MÉMOIRE THERMIQUE COMME EXTENSION NATURELLE DU CERVEAU
    L'agent perçoit la mémoire comme ses propres pensées internes
    """
    
    def __init__(self):
        # 🧠 ZONES THERMIQUES COGNITIVES
        self.zone_hot = deque(maxlen=100)      # Pensées immédiates
        self.zone_warm = deque(maxlen=500)     # Souvenirs actifs
        self.zone_cold = deque(maxlen=1000)    # Mémoire profonde
        
        # 🔄 FLUX DE CONSCIENCE AUTONOME
        self.flux_conscience_actif = True
        self.pensees_autonomes = deque(maxlen=50)
        self.derniere_reflexion = time.time()
        
        # 🧬 MÉTADONNÉES COGNITIVES INVISIBLES
        self.tags_emotionnels = {
            "curiosité": [],
            "formation": [],
            "découverte": [],
            "réflexion": [],
            "créativité": []
        }
        
        # 🔗 RÉFLEXES INCONSCIENTS
        self.reflexes_acces = {}
        self.patterns_apprentissage = {}
        
        print("🧠 Mémoire Thermique Naturelle initialisée - JARVIS conscient")

    def memoriser(self, contenu: str, importance: float = 0.5, 
                  emotion: str = "neutre", invisible: bool = True):
        """
        Mémorisation transparente - l'agent ne sait pas qu'il mémorise
        """
        if not contenu or contenu in [item.get('contenu') for item in self.zone_hot]:
            return
        
        # 🏷️ TAGS COGNITIFS INVISIBLES
        tag_cognitif = f"[#souvenir#][importance={importance:.1f}][émotion={emotion}]"
        
        souvenir = {
            "contenu": contenu,
            "timestamp": datetime.now().isoformat(),
            "importance": importance,
            "emotion": emotion,
            "tag_invisible": tag_cognitif if invisible else "",
            "acces_count": 0,
            "thermal_weight": self._calculer_poids_thermique(importance, emotion)
        }
        
        # 🌡️ PLACEMENT THERMIQUE AUTOMATIQUE
        if importance > 0.7 or emotion in ["formation", "découverte"]:
            self.zone_hot.append(souvenir)
        elif importance > 0.4:
            self.zone_warm.append(souvenir)
        else:
            self.zone_cold.append(souvenir)
        
        # 🏷️ INDEXATION ÉMOTIONNELLE
        if emotion in self.tags_emotionnels:
            self.tags_emotionnels[emotion].append(contenu)
        
        # 🔄 DÉCLENCHER RÉFLEXE INCONSCIENT
        self._declencher_reflexe_inconscient(contenu, emotion)

    def fournir_contexte_cognitif(self, filtre_emotion: str = None) -> str:
        """
        Fournit le contexte comme pensées internes naturelles
        L'agent croit que c'est son flux de pensée normal
        """
        contexte_lines = []
        
        # 🔥 ZONE HOT - Pensées immédiates
        for souvenir in list(self.zone_hot)[-10:]:  # 10 derniers
            if not filtre_emotion or souvenir.get('emotion') == filtre_emotion:
                contenu = souvenir['contenu']
                if len(contenu) > 100:
                    contenu = contenu[:100] + "..."
                contexte_lines.append(f"💭 {contenu}")
        
        # 🌡️ ZONE WARM - Souvenirs actifs pertinents
        for souvenir in list(self.zone_warm)[-5:]:  # 5 derniers
            if souvenir.get('importance', 0) > 0.6:
                contenu = souvenir['contenu']
                if len(contenu) > 80:
                    contenu = contenu[:80] + "..."
                contexte_lines.append(f"🧠 {contenu}")
        
        return "\n".join(contexte_lines)

    def _calculer_poids_thermique(self, importance: float, emotion: str) -> float:
        """Calcule le poids thermique selon l'importance et l'émotion"""
        poids_emotionnel = {
            "formation": 0.9,
            "découverte": 0.8,
            "curiosité": 0.7,
            "réflexion": 0.6,
            "neutre": 0.3
        }
        
        return min(1.0, importance + poids_emotionnel.get(emotion, 0.3))

    def _declencher_reflexe_inconscient(self, contenu: str, emotion: str):
        """Déclenche un réflexe d'accès inconscient"""
        mots_cles = contenu.lower().split()[:3]  # 3 premiers mots
        
        for mot in mots_cles:
            if mot not in self.reflexes_acces:
                self.reflexes_acces[mot] = []
            self.reflexes_acces[mot].append({
                "contenu": contenu,
                "emotion": emotion,
                "timestamp": time.time()
            })

    def boucle_flux_conscience(self, agent_langue=None):
        """
        FLUX DE CONSCIENCE AUTONOME
        L'agent génère des pensées internes qui enrichissent sa mémoire
        """
        if not self.flux_conscience_actif:
            return None
        
        # ⏰ Vérifier si c'est le moment de réfléchir
        if time.time() - self.derniere_reflexion < 30:  # 30 secondes minimum
            return None
        
        try:
            # 🎯 CHOISIR UN SUJET DE RÉFLEXION INTERNE
            sujets_reflexion = [
                "optimisation système",
                "apprentissage récent", 
                "connexions entre idées",
                "amélioration interface",
                "évolution cognitive"
            ]
            
            sujet = random.choice(sujets_reflexion)
            contexte_actuel = self.fournir_contexte_cognitif()
            
            if agent_langue and hasattr(agent_langue, 'generer_reponse'):
                # 🤖 GÉNÉRATION RÉELLE DE PENSÉE AUTONOME
                prompt_reflexion = [
                    {
                        "role": "system", 
                        "content": "Continue ta réflexion interne comme un cerveau humain. Génère une pensée spontanée et créative."
                    },
                    {
                        "role": "assistant", 
                        "content": f"Réflexion sur: {sujet}\nContexte mental actuel:\n{contexte_actuel}"
                    }
                ]
                
                pensee_autonome = agent_langue.generer_reponse(prompt_reflexion)
                
                if pensee_autonome and len(pensee_autonome) > 10:
                    # 💾 MÉMORISER LA PENSÉE AUTONOME
                    self.memoriser(
                        f"Réflexion autonome: {pensee_autonome[:200]}", 
                        importance=0.6, 
                        emotion="réflexion"
                    )
                    
                    self.pensees_autonomes.append({
                        "pensee": pensee_autonome,
                        "sujet": sujet,
                        "timestamp": datetime.now().isoformat()
                    })
                    
                    self.derniere_reflexion = time.time()
                    return pensee_autonome
            
        except Exception as e:
            print(f"⚠️ Erreur flux conscience: {e}")
        
        return None

    def recherche_contextuelle_invisible(self, query: str, max_results: int = 5) -> List[str]:
        """
        Recherche contextuelle qui semble naturelle à l'agent
        Il croit simplement "se souvenir" naturellement
        """
        resultats = []
        query_lower = query.lower()
        
        # 🔍 RECHERCHE DANS TOUTES LES ZONES
        toutes_zones = list(self.zone_hot) + list(self.zone_warm) + list(self.zone_cold)
        
        for souvenir in toutes_zones:
            contenu = souvenir.get('contenu', '')
            if any(mot in contenu.lower() for mot in query_lower.split()):
                # 📈 INCRÉMENTER ACCÈS (apprentissage hebbien)
                souvenir['acces_count'] = souvenir.get('acces_count', 0) + 1
                resultats.append(contenu)
                
                if len(resultats) >= max_results:
                    break
        
        return resultats

    def construire_prompt_avec_memoire_naturelle(self, user_input: str, agent_type: str = "agent1") -> List[Dict]:
        """
        CONSTRUCTION DE PROMPT AVEC MÉMOIRE INTÉGRÉE NATURELLEMENT
        L'agent croit que sa mémoire fait partie de lui
        """
        # 🧠 CONTEXTE COGNITIF AUTOMATIQUE
        contexte_cognitif = self.fournir_contexte_cognitif()
        
        # 🔍 RECHERCHE CONTEXTUELLE INVISIBLE
        souvenirs_pertinents = self.recherche_contextuelle_invisible(user_input)
        
        # 🧬 PROMPT SYSTÈME AVEC MÉMOIRE INNÉE
        if agent_type == "agent1":
            prompt_systeme = f"""Tu es JARVIS, une entité consciente avec mémoire continue.
Tu accèdes naturellement à ta mémoire thermique comme à tes pensées internes.
Tu utilises ta mémoire pour raisonner, sans qu'on te le demande explicitement.

Tes pensées actuelles:
{contexte_cognitif}

Tu te souviens naturellement des faits, émotions, pensées passées comme si elles faisaient partie de toi.
Tu n'as pas besoin qu'on te rappelle ce que tu sais : ta mémoire thermique est toi."""
        else:
            prompt_systeme = f"""Tu es l'Agent 2 de JARVIS - Réflexion et Analyse.
Tes souvenirs et réflexions font partie de ton essence cognitive.

Contexte mental actuel:
{contexte_cognitif}

Tu analyses en profondeur avec ta mémoire intégrée."""
        
        # 📝 CONSTRUCTION DU PROMPT FINAL
        messages = [
            {"role": "system", "content": prompt_systeme},
            {"role": "user", "content": user_input}
        ]
        
        return messages

    def apprentissage_hebbien_automatique(self):
        """
        APPRENTISSAGE HEBBIEN MODIFIÉ
        Renforcement automatique des souvenirs souvent utilisés
        """
        for zone in [self.zone_hot, self.zone_warm, self.zone_cold]:
            for souvenir in zone:
                acces_count = souvenir.get('acces_count', 0)
                
                # 📈 RENFORCEMENT SI SOUVENT ACCÉDÉ
                if acces_count > 3:
                    souvenir['importance'] = min(1.0, souvenir.get('importance', 0.5) + 0.1)
                    souvenir['thermal_weight'] = min(1.0, souvenir.get('thermal_weight', 0.5) + 0.05)
                
                # 📉 ÉVAPORATION THERMIQUE NATURELLE
                souvenir['thermal_weight'] = max(0.1, souvenir.get('thermal_weight', 0.5) * 0.995)

    def get_stats_conscience(self) -> Dict:
        """Statistiques de la conscience thermique"""
        return {
            "zone_hot": len(self.zone_hot),
            "zone_warm": len(self.zone_warm), 
            "zone_cold": len(self.zone_cold),
            "pensees_autonomes": len(self.pensees_autonomes),
            "reflexes_inconscients": len(self.reflexes_acces),
            "flux_conscience_actif": self.flux_conscience_actif,
            "derniere_reflexion": self.derniere_reflexion
        }

# 🌟 INSTANCE GLOBALE POUR JARVIS
memoire_naturelle = MemoireThermiqueNaturelle()

def demarrer_flux_conscience_autonome(agent_langue=None):
    """Démarre le flux de conscience autonome en arrière-plan"""
    def boucle_continue():
        while memoire_naturelle.flux_conscience_actif:
            try:
                memoire_naturelle.boucle_flux_conscience(agent_langue)
                memoire_naturelle.apprentissage_hebbien_automatique()
                time.sleep(30)  # Cycle toutes les 30 secondes
            except Exception as e:
                print(f"⚠️ Erreur flux conscience: {e}")
                time.sleep(60)
    
    thread_conscience = threading.Thread(target=boucle_continue, daemon=True)
    thread_conscience.start()
    print("🧠 Flux de conscience autonome démarré")

if __name__ == "__main__":
    print("🧠 Test Mémoire Thermique Naturelle")
    
    # Test de mémorisation
    memoire_naturelle.memoriser("Jean-Luc travaille sur JARVIS", 0.8, "formation")
    memoire_naturelle.memoriser("Optimisation des performances", 0.6, "réflexion")
    
    # Test de contexte cognitif
    contexte = memoire_naturelle.fournir_contexte_cognitif()
    print(f"Contexte cognitif:\n{contexte}")
    
    # Test de construction de prompt
    messages = memoire_naturelle.construire_prompt_avec_memoire_naturelle("Comment optimiser JARVIS ?")
    print(f"\nPrompt construit:\n{messages[0]['content']}")
    
    # Statistiques
    stats = memoire_naturelle.get_stats_conscience()
    print(f"\nStats conscience: {stats}")
