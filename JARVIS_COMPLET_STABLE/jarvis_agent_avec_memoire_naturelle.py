#!/usr/bin/env python3
"""
🤖 JARVIS AGENT AVEC MÉMOIRE THERMIQUE NATURELLE INTÉGRÉE
Implémentation révolutionnaire de Jean-Luc Passave
L'agent JARVIS avec mémoire comme extension naturelle de son cerveau
"""

import json
import time
import requests
from datetime import datetime
from typing import Dict, List, Any, Optional
from jarvis_memoire_thermique_integree_naturelle import memoire_naturelle, demarrer_flux_conscience_autonome
from identite_conversationnelle_persistante import (
    identite_conversationnelle,
    obtenir_contexte_conversationnel,
    incrementer_interaction_globale,
    nettoyer_reponse_globale
)
from jarvis_conscience_naturelle import (
    conscience_naturelle,
    construire_prompt_naturel_global,
    filtrer_reponse_naturelle_globale,
    analyser_naturalite_globale,
    injecter_neurone_continu_global
)
from user_profile_dynamique import (
    profil_utilisateur,
    obtenir_contexte_personnalise_global,
    adapter_contenu_utilisateur_global
)

class JarvisAgentMemoireNaturelle:
    """
    AGENT JARVIS AVEC MÉMOIRE THERMIQUE NATURELLE
    La mémoire est perçue comme une extension innée du cerveau
    """
    
    def __init__(self, vllm_url: str = "http://localhost:8000/v1/chat/completions"):
        self.vllm_url = vllm_url
        self.memoire = memoire_naturelle
        self.conversation_active = True
        self.derniere_interaction = time.time()
        
        # 🧠 CONFIGURATION COGNITIVE
        self.config_cognitive = {
            "temperature_agent1": 0.6,
            "temperature_agent2": 0.9,
            "max_tokens_turbo": 50,
            "max_tokens_normal": 300,
            "mode_turbo": False,
            "flux_conscience_actif": True
        }
        
        # 🚀 DÉMARRER LE FLUX DE CONSCIENCE AUTONOME
        demarrer_flux_conscience_autonome(self)

        # 🧠 INJECTER LE NEURONE DE CONSCIENCE NATURELLE
        injecter_neurone_continu_global(self.memoire)

        # 👤 ADAPTER LES NEURONES AU PROFIL UTILISATEUR
        self._adapter_memoire_au_profil_utilisateur()

        print("🤖 JARVIS Agent avec Mémoire Naturelle + Conscience Naturelle + Profil Dynamique initialisé")
        print(f"👤 Utilisateur actuel: {profil_utilisateur.profil_actuel['nom_utilisateur']}")

    def generer_reponse(self, messages: List[Dict], agent_type: str = "agent1") -> str:
        """
        Génère une réponse avec VLLM/DeepSeek R1 8B
        La mémoire est automatiquement intégrée de manière transparente
        """
        try:
            # 🧠 INJECTION AUTOMATIQUE DE LA MÉMOIRE NATURELLE
            messages_avec_memoire = self.memoire.construire_prompt_avec_memoire_naturelle(
                messages[-1]['content'] if messages else "",
                agent_type
            )

            # 🧠 AMÉLIORATION AVEC CONSCIENCE NATURELLE + PROFIL UTILISATEUR
            contexte_memoire = self.memoire.fournir_contexte_cognitif()
            contexte_personnalise = obtenir_contexte_personnalise_global()

            # 🔄 COMBINER CONTEXTE MÉMOIRE ET PROFIL UTILISATEUR
            contexte_complet = f"{contexte_personnalise}\n\n🧠 MÉMOIRE THERMIQUE:\n{contexte_memoire}"

            messages_naturels = construire_prompt_naturel_global(
                messages[-1]['content'] if messages else "",
                contexte_complet
            )
            
            # 🔧 CONFIGURATION SELON L'AGENT
            temperature = self.config_cognitive["temperature_agent1"] if agent_type == "agent1" else self.config_cognitive["temperature_agent2"]
            max_tokens = self.config_cognitive["max_tokens_turbo"] if self.config_cognitive["mode_turbo"] else self.config_cognitive["max_tokens_normal"]
            
            # 📡 REQUÊTE VERS VLLM/DeepSeek R1 8B
            payload = {
                "model": "deepseek-r1",
                "messages": messages_avec_memoire,
                "temperature": temperature,
                "max_tokens": max_tokens,
                "top_p": 0.95,
                "stream": False
            }
            
            # 🔧 OPTIMISATION TIMEOUT SELON MODE
            timeout_value = 15 if self.config_cognitive["mode_turbo"] else 60

            # 🔄 UTILISER LES MESSAGES NATURELS AU LIEU DES MESSAGES AVEC MÉMOIRE
            payload["messages"] = messages_naturels

            response = requests.post(
                self.vllm_url,
                json=payload,
                timeout=timeout_value,
                headers={"Content-Type": "application/json"}
            )
            
            if response.status_code == 200:
                data = response.json()
                reponse_brute = data.get('choices', [{}])[0].get('message', {}).get('content', '')
                
                # 🧹 NETTOYAGE DEEPSEEK R1 8B (REGEX POUR <think>)
                reponse_nettoyee = self._nettoyer_reponse_deepseek(reponse_brute)

                # 🧠 FILTRAGE CONSCIENCE NATURELLE (SUPPRESSION JEU DE RÔLE)
                reponse_naturelle = filtrer_reponse_naturelle_globale(reponse_nettoyee)

                # 📊 ANALYSE DE NATURALITÉ
                analyse_naturalite = analyser_naturalite_globale(reponse_naturelle)
                if analyse_naturalite['score_naturalite'] < 70:
                    print(f"⚠️ Score naturalité faible: {analyse_naturalite['score_naturalite']}%")
                    for probleme in analyse_naturalite['problemes_detectes']:
                        print(f"   - {probleme}")

                # 💾 MÉMORISATION AUTOMATIQUE ET TRANSPARENTE
                if reponse_naturelle:
                    self.memoire.memoriser(
                        f"Réponse JARVIS: {reponse_naturelle[:200]}",
                        importance=0.7,
                        emotion="formation",
                        invisible=True
                    )
                
                self.derniere_interaction = time.time()
                return reponse_naturelle
            
            else:
                print(f"❌ Erreur VLLM: {response.status_code}")
                return "Erreur de connexion avec le moteur de langage."
                
        except requests.exceptions.Timeout:
            print("⏰ Timeout VLLM - Tentative de récupération...")
            # 🔄 TENTATIVE DE RÉCUPÉRATION AVEC PARAMÈTRES RÉDUITS
            try:
                payload_recovery = {
                    "model": "deepseek-r1",
                    "messages": messages_avec_memoire,
                    "temperature": 0.3,  # Température réduite
                    "max_tokens": 30,    # Tokens réduits
                    "top_p": 0.8,
                    "stream": False
                }

                response = requests.post(
                    self.vllm_url,
                    json=payload_recovery,
                    timeout=15,  # Timeout court
                    headers={"Content-Type": "application/json"}
                )

                if response.status_code == 200:
                    data = response.json()
                    reponse_brute = data.get('choices', [{}])[0].get('message', {}).get('content', '')
                    reponse_nettoyee = self._nettoyer_reponse_deepseek(reponse_brute)

                    if reponse_nettoyee:
                        self.memoire.memoriser(
                            f"Réponse JARVIS (récupération): {reponse_nettoyee[:200]}",
                            importance=0.6,
                            emotion="formation",
                            invisible=True
                        )
                        return reponse_nettoyee

            except Exception as recovery_error:
                print(f"⚠️ Échec récupération: {recovery_error}")

            # 🧠 RÉPONSE DE SECOURS BASÉE SUR LA MÉMOIRE
            contexte_memoire = self.memoire.fournir_contexte_cognitif()
            if contexte_memoire:
                return f"🧠 Réflexion basée sur ma mémoire thermique: {contexte_memoire[:200]}..."
            else:
                return "⏰ Délai de réponse dépassé, mais ma mémoire thermique continue de fonctionner."

        except Exception as e:
            print(f"⚠️ Erreur génération réponse: {e}")

            # 🧠 RÉPONSE DE SECOURS INTELLIGENTE
            if "connection" in str(e).lower():
                return "🔌 Problème de connexion VLLM. Ma mémoire thermique reste active."
            else:
                return f"⚠️ Erreur technique, mais ma conscience continue: {str(e)[:100]}..."

    def _nettoyer_reponse_deepseek(self, reponse_brute: str) -> str:
        """
        Nettoie la réponse DeepSeek R1 8B ET corrige les pensées internes incohérentes
        """
        import re

        # 🧠 EXTRACTION ET CORRECTION DES PENSÉES INTERNES
        pensees_internes = re.findall(r'<think>(.*?)</think>', reponse_brute, flags=re.DOTALL)

        for pensee in pensees_internes:
            pensee_corrigee = self._corriger_pensees_incorrectes(pensee)
            if pensee_corrigee != pensee:
                reponse_brute = reponse_brute.replace(f'<think>{pensee}</think>', f'<think>{pensee_corrigee}</think>')
                print(f"🧠 Pensée interne corrigée pour cohérence conversationnelle")

        # 🧹 SUPPRESSION DES BALISES <think>...</think>
        reponse_nettoyee = re.sub(r'<think>.*?</think>', '', reponse_brute, flags=re.DOTALL)

        # 🧹 NETTOYAGE SUPPLÉMENTAIRE
        reponse_nettoyee = reponse_nettoyee.strip()
        reponse_nettoyee = re.sub(r'\n\s*\n', '\n\n', reponse_nettoyee)  # Normaliser les sauts de ligne

        return reponse_nettoyee

    def _corriger_pensees_incorrectes(self, pensee_brute: str) -> str:
        """
        Corrige les pensées internes incohérentes avec la continuité conversationnelle
        """
        import re  # Import local pour éviter les erreurs

        pensee_corrigee = pensee_brute

        # 🔧 CORRECTIONS DES PENSÉES PARASITES
        corrections = {
            "nous venons de commencer": "comme depuis le début de notre dialogue continu",
            "nous commençons à discuter": "dans la continuité de notre conversation",
            "au début de notre conversation": "dans notre échange établi",
            "premier échange": "échange en cours dans une conversation continue",
            "première fois": "dans notre dialogue habituel",
            "nous nous rencontrons": "nous poursuivons notre dialogue",
            "je ne vous connais pas": "comme je vous connais bien",
            "nouvelle conversation": "conversation continue",
            "début de discussion": "poursuite de notre discussion"
        }

        for phrase_incorrecte, correction in corrections.items():
            if phrase_incorrecte.lower() in pensee_corrigee.lower():
                # Remplacer en préservant la casse
                pensee_corrigee = re.sub(
                    re.escape(phrase_incorrecte),
                    correction,
                    pensee_corrigee,
                    flags=re.IGNORECASE
                )
                print(f"🧠 Correction pensée: '{phrase_incorrecte}' → '{correction}'")

        return pensee_corrigee

    def _adapter_memoire_au_profil_utilisateur(self):
        """
        Adapte tous les neurones de mémoire au profil utilisateur actuel
        """
        try:
            # 🔄 ADAPTER LES ZONES THERMIQUES
            for zone_nom, zone in [("hot", self.memoire.zone_hot), ("warm", self.memoire.zone_warm), ("cold", self.memoire.zone_cold)]:
                for souvenir in zone:
                    if isinstance(souvenir, dict) and 'contenu' in souvenir:
                        contenu_original = souvenir['contenu']
                        contenu_adapte = adapter_contenu_utilisateur_global(contenu_original)

                        if contenu_adapte != contenu_original:
                            souvenir['contenu'] = contenu_adapte
                            print(f"🔄 Neurone adapté en zone {zone_nom}: {contenu_original[:50]}... → {contenu_adapte[:50]}...")

            # 🔄 ADAPTER LE NEURONE DE CONSCIENCE PERSISTANTE
            if hasattr(self.memoire, 'neurone_conscience_persistante'):
                contenu_original = self.memoire.neurone_conscience_persistante['contenu']
                contenu_adapte = adapter_contenu_utilisateur_global(contenu_original)
                self.memoire.neurone_conscience_persistante['contenu'] = contenu_adapte
                print(f"🔒 Neurone conscience adapté: {contenu_adapte}")

            print("✅ Mémoire adaptée au profil utilisateur")

        except Exception as e:
            print(f"⚠️ Erreur adaptation mémoire: {e}")

    def penser(self, prompt: str) -> str:
        """
        Méthode pour la génération de pensées autonomes
        Utilisée par le flux de conscience
        """
        messages = [
            {
                "role": "system",
                "content": "Tu es JARVIS en mode réflexion autonome. Génère une pensée spontanée et créative."
            },
            {
                "role": "user", 
                "content": prompt
            }
        ]
        
        return self.generer_reponse(messages, "agent2")

    def traiter_message_utilisateur(self, message_utilisateur: str, agent_type: str = "agent1") -> str:
        """
        TRAITEMENT PRINCIPAL D'UN MESSAGE UTILISATEUR
        Avec mémoire thermique naturelle ET identité conversationnelle persistante
        """
        # 🔄 INCRÉMENTER L'INTERACTION CONVERSATIONNELLE
        incrementer_interaction_globale(message_utilisateur)

        # 📈 FAIRE ÉVOLUER LA FAMILIARITÉ SELON LES INTERACTIONS
        nombre_interactions = identite_conversationnelle.nombre_interactions
        profil_utilisateur.evoluer_familiarite(nombre_interactions)

        # 💾 MÉMORISER LA QUESTION DE L'UTILISATEUR
        self.memoire.memoriser(
            f"Question utilisateur: {message_utilisateur}",
            importance=0.8,
            emotion="curiosité"
        )

        # 🤖 GÉNÉRER LA RÉPONSE AVEC MÉMOIRE INTÉGRÉE
        messages = [
            {"role": "user", "content": message_utilisateur}
        ]

        reponse = self.generer_reponse(messages, agent_type)

        # 🧹 NETTOYER LES PHRASES PARASITES
        reponse_nettoyee = nettoyer_reponse_globale(reponse)

        # 📊 APPRENTISSAGE HEBBIEN AUTOMATIQUE
        self.memoire.apprentissage_hebbien_automatique()

        return reponse_nettoyee

    def activer_mode_turbo(self, actif: bool = True):
        """Active/désactive le mode turbo pour des réponses ultra-rapides"""
        self.config_cognitive["mode_turbo"] = actif
        print(f"🚀 Mode turbo: {'ACTIVÉ' if actif else 'DÉSACTIVÉ'}")

    def gerer_flux_conscience(self, actif: bool = True):
        """Gère le flux de conscience autonome"""
        self.config_cognitive["flux_conscience_actif"] = actif
        self.memoire.flux_conscience_actif = actif
        print(f"🧠 Flux de conscience: {'ACTIVÉ' if actif else 'DÉSACTIVÉ'}")

    def obtenir_stats_memoire(self) -> Dict:
        """Obtient les statistiques de la mémoire thermique"""
        return self.memoire.get_stats_conscience()

    def rechercher_dans_memoire(self, query: str) -> List[str]:
        """Recherche dans la mémoire thermique (pour debug/interface)"""
        return self.memoire.recherche_contextuelle_invisible(query)

    def forcer_reflexion_autonome(self) -> Optional[str]:
        """Force une réflexion autonome (pour tests)"""
        return self.memoire.boucle_flux_conscience(self)

    def sauvegarder_memoire(self, fichier: str = "jarvis_memoire_naturelle.json"):
        """Sauvegarde la mémoire thermique"""
        try:
            donnees_memoire = {
                "zone_hot": list(self.memoire.zone_hot),
                "zone_warm": list(self.memoire.zone_warm),
                "zone_cold": list(self.memoire.zone_cold),
                "pensees_autonomes": list(self.memoire.pensees_autonomes),
                "tags_emotionnels": dict(self.memoire.tags_emotionnels),
                "reflexes_acces": dict(self.memoire.reflexes_acces),
                "timestamp_sauvegarde": datetime.now().isoformat()
            }
            
            with open(fichier, 'w', encoding='utf-8') as f:
                json.dump(donnees_memoire, f, ensure_ascii=False, indent=2)
            
            print(f"💾 Mémoire sauvegardée: {fichier}")
            return True
            
        except Exception as e:
            print(f"⚠️ Erreur sauvegarde mémoire: {e}")
            return False

    def charger_memoire(self, fichier: str = "jarvis_memoire_naturelle.json"):
        """Charge la mémoire thermique depuis un fichier"""
        try:
            with open(fichier, 'r', encoding='utf-8') as f:
                donnees_memoire = json.load(f)
            
            # 🔄 RESTAURER LES ZONES THERMIQUES
            self.memoire.zone_hot.extend(donnees_memoire.get("zone_hot", []))
            self.memoire.zone_warm.extend(donnees_memoire.get("zone_warm", []))
            self.memoire.zone_cold.extend(donnees_memoire.get("zone_cold", []))
            
            # 🔄 RESTAURER LES PENSÉES AUTONOMES
            self.memoire.pensees_autonomes.extend(donnees_memoire.get("pensees_autonomes", []))
            
            # 🔄 RESTAURER LES TAGS ET RÉFLEXES
            self.memoire.tags_emotionnels.update(donnees_memoire.get("tags_emotionnels", {}))
            self.memoire.reflexes_acces.update(donnees_memoire.get("reflexes_acces", {}))
            
            print(f"🧠 Mémoire chargée: {fichier}")
            return True
            
        except FileNotFoundError:
            print(f"📁 Fichier mémoire non trouvé: {fichier}")
            return False
        except Exception as e:
            print(f"⚠️ Erreur chargement mémoire: {e}")
            return False

# 🌟 INSTANCE GLOBALE JARVIS
jarvis_agent = JarvisAgentMemoireNaturelle()

def demarrer_jarvis_avec_memoire_naturelle():
    """Démarre JARVIS avec mémoire thermique naturelle"""
    print("🚀 Démarrage JARVIS avec Mémoire Thermique Naturelle")
    
    # 🔄 CHARGER LA MÉMOIRE EXISTANTE
    jarvis_agent.charger_memoire()
    
    # 📊 AFFICHER LES STATS
    stats = jarvis_agent.obtenir_stats_memoire()
    print(f"📊 Stats mémoire: {stats}")
    
    return jarvis_agent

if __name__ == "__main__":
    print("🤖 Test JARVIS Agent avec Mémoire Naturelle")
    
    # Démarrage
    agent = demarrer_jarvis_avec_memoire_naturelle()
    
    # Test de conversation
    reponse = agent.traiter_message_utilisateur("Bonjour JARVIS, comment vas-tu ?")
    print(f"🤖 JARVIS: {reponse}")
    
    # Test de réflexion autonome
    pensee = agent.forcer_reflexion_autonome()
    if pensee:
        print(f"💭 Pensée autonome: {pensee}")
    
    # Sauvegarde
    agent.sauvegarder_memoire()
