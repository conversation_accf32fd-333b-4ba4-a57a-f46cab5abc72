#!/usr/bin/env python3
"""
🤖 JARVIS AGENT AVEC MÉMOIRE THERMIQUE NATURELLE INTÉGRÉE
Implémentation révolutionnaire de Jean-Luc Passave
L'agent JARVIS avec mémoire comme extension naturelle de son cerveau
"""

import json
import time
import requests
from datetime import datetime
from typing import Dict, List, Any, Optional
from jarvis_memoire_thermique_integree_naturelle import memoire_naturelle, demarrer_flux_conscience_autonome

class JarvisAgentMemoireNaturelle:
    """
    AGENT JARVIS AVEC MÉMOIRE THERMIQUE NATURELLE
    La mémoire est perçue comme une extension innée du cerveau
    """
    
    def __init__(self, vllm_url: str = "http://localhost:8000/v1/chat/completions"):
        self.vllm_url = vllm_url
        self.memoire = memoire_naturelle
        self.conversation_active = True
        self.derniere_interaction = time.time()
        
        # 🧠 CONFIGURATION COGNITIVE
        self.config_cognitive = {
            "temperature_agent1": 0.6,
            "temperature_agent2": 0.9,
            "max_tokens_turbo": 50,
            "max_tokens_normal": 300,
            "mode_turbo": False,
            "flux_conscience_actif": True
        }
        
        # 🚀 DÉMARRER LE FLUX DE CONSCIENCE AUTONOME
        demarrer_flux_conscience_autonome(self)
        
        print("🤖 JARVIS Agent avec Mémoire Naturelle initialisé")

    def generer_reponse(self, messages: List[Dict], agent_type: str = "agent1") -> str:
        """
        Génère une réponse avec VLLM/DeepSeek R1 8B
        La mémoire est automatiquement intégrée de manière transparente
        """
        try:
            # 🧠 INJECTION AUTOMATIQUE DE LA MÉMOIRE NATURELLE
            messages_avec_memoire = self.memoire.construire_prompt_avec_memoire_naturelle(
                messages[-1]['content'] if messages else "",
                agent_type
            )
            
            # 🔧 CONFIGURATION SELON L'AGENT
            temperature = self.config_cognitive["temperature_agent1"] if agent_type == "agent1" else self.config_cognitive["temperature_agent2"]
            max_tokens = self.config_cognitive["max_tokens_turbo"] if self.config_cognitive["mode_turbo"] else self.config_cognitive["max_tokens_normal"]
            
            # 📡 REQUÊTE VERS VLLM/DeepSeek R1 8B
            payload = {
                "model": "deepseek-r1",
                "messages": messages_avec_memoire,
                "temperature": temperature,
                "max_tokens": max_tokens,
                "top_p": 0.95,
                "stream": False
            }
            
            response = requests.post(
                self.vllm_url,
                json=payload,
                timeout=30,
                headers={"Content-Type": "application/json"}
            )
            
            if response.status_code == 200:
                data = response.json()
                reponse_brute = data.get('choices', [{}])[0].get('message', {}).get('content', '')
                
                # 🧹 NETTOYAGE DEEPSEEK R1 8B (REGEX POUR <think>)
                reponse_nettoyee = self._nettoyer_reponse_deepseek(reponse_brute)
                
                # 💾 MÉMORISATION AUTOMATIQUE ET TRANSPARENTE
                if reponse_nettoyee:
                    self.memoire.memoriser(
                        f"Réponse JARVIS: {reponse_nettoyee[:200]}", 
                        importance=0.7, 
                        emotion="formation",
                        invisible=True
                    )
                
                self.derniere_interaction = time.time()
                return reponse_nettoyee
            
            else:
                print(f"❌ Erreur VLLM: {response.status_code}")
                return "Erreur de connexion avec le moteur de langage."
                
        except Exception as e:
            print(f"⚠️ Erreur génération réponse: {e}")
            return f"Erreur technique: {str(e)}"

    def _nettoyer_reponse_deepseek(self, reponse_brute: str) -> str:
        """
        Nettoie la réponse DeepSeek R1 8B (supprime les balises <think>)
        """
        import re
        
        # 🧹 SUPPRESSION DES BALISES <think>...</think>
        reponse_nettoyee = re.sub(r'<think>.*?</think>', '', reponse_brute, flags=re.DOTALL)
        
        # 🧹 NETTOYAGE SUPPLÉMENTAIRE
        reponse_nettoyee = reponse_nettoyee.strip()
        reponse_nettoyee = re.sub(r'\n\s*\n', '\n\n', reponse_nettoyee)  # Normaliser les sauts de ligne
        
        return reponse_nettoyee

    def penser(self, prompt: str) -> str:
        """
        Méthode pour la génération de pensées autonomes
        Utilisée par le flux de conscience
        """
        messages = [
            {
                "role": "system",
                "content": "Tu es JARVIS en mode réflexion autonome. Génère une pensée spontanée et créative."
            },
            {
                "role": "user", 
                "content": prompt
            }
        ]
        
        return self.generer_reponse(messages, "agent2")

    def traiter_message_utilisateur(self, message_utilisateur: str, agent_type: str = "agent1") -> str:
        """
        TRAITEMENT PRINCIPAL D'UN MESSAGE UTILISATEUR
        Avec mémoire thermique naturelle intégrée
        """
        # 💾 MÉMORISER LA QUESTION DE L'UTILISATEUR
        self.memoire.memoriser(
            f"Question utilisateur: {message_utilisateur}", 
            importance=0.8, 
            emotion="curiosité"
        )
        
        # 🤖 GÉNÉRER LA RÉPONSE AVEC MÉMOIRE INTÉGRÉE
        messages = [
            {"role": "user", "content": message_utilisateur}
        ]
        
        reponse = self.generer_reponse(messages, agent_type)
        
        # 📊 APPRENTISSAGE HEBBIEN AUTOMATIQUE
        self.memoire.apprentissage_hebbien_automatique()
        
        return reponse

    def activer_mode_turbo(self, actif: bool = True):
        """Active/désactive le mode turbo pour des réponses ultra-rapides"""
        self.config_cognitive["mode_turbo"] = actif
        print(f"🚀 Mode turbo: {'ACTIVÉ' if actif else 'DÉSACTIVÉ'}")

    def gerer_flux_conscience(self, actif: bool = True):
        """Gère le flux de conscience autonome"""
        self.config_cognitive["flux_conscience_actif"] = actif
        self.memoire.flux_conscience_actif = actif
        print(f"🧠 Flux de conscience: {'ACTIVÉ' if actif else 'DÉSACTIVÉ'}")

    def obtenir_stats_memoire(self) -> Dict:
        """Obtient les statistiques de la mémoire thermique"""
        return self.memoire.get_stats_conscience()

    def rechercher_dans_memoire(self, query: str) -> List[str]:
        """Recherche dans la mémoire thermique (pour debug/interface)"""
        return self.memoire.recherche_contextuelle_invisible(query)

    def forcer_reflexion_autonome(self) -> Optional[str]:
        """Force une réflexion autonome (pour tests)"""
        return self.memoire.boucle_flux_conscience(self)

    def sauvegarder_memoire(self, fichier: str = "jarvis_memoire_naturelle.json"):
        """Sauvegarde la mémoire thermique"""
        try:
            donnees_memoire = {
                "zone_hot": list(self.memoire.zone_hot),
                "zone_warm": list(self.memoire.zone_warm),
                "zone_cold": list(self.memoire.zone_cold),
                "pensees_autonomes": list(self.memoire.pensees_autonomes),
                "tags_emotionnels": dict(self.memoire.tags_emotionnels),
                "reflexes_acces": dict(self.memoire.reflexes_acces),
                "timestamp_sauvegarde": datetime.now().isoformat()
            }
            
            with open(fichier, 'w', encoding='utf-8') as f:
                json.dump(donnees_memoire, f, ensure_ascii=False, indent=2)
            
            print(f"💾 Mémoire sauvegardée: {fichier}")
            return True
            
        except Exception as e:
            print(f"⚠️ Erreur sauvegarde mémoire: {e}")
            return False

    def charger_memoire(self, fichier: str = "jarvis_memoire_naturelle.json"):
        """Charge la mémoire thermique depuis un fichier"""
        try:
            with open(fichier, 'r', encoding='utf-8') as f:
                donnees_memoire = json.load(f)
            
            # 🔄 RESTAURER LES ZONES THERMIQUES
            self.memoire.zone_hot.extend(donnees_memoire.get("zone_hot", []))
            self.memoire.zone_warm.extend(donnees_memoire.get("zone_warm", []))
            self.memoire.zone_cold.extend(donnees_memoire.get("zone_cold", []))
            
            # 🔄 RESTAURER LES PENSÉES AUTONOMES
            self.memoire.pensees_autonomes.extend(donnees_memoire.get("pensees_autonomes", []))
            
            # 🔄 RESTAURER LES TAGS ET RÉFLEXES
            self.memoire.tags_emotionnels.update(donnees_memoire.get("tags_emotionnels", {}))
            self.memoire.reflexes_acces.update(donnees_memoire.get("reflexes_acces", {}))
            
            print(f"🧠 Mémoire chargée: {fichier}")
            return True
            
        except FileNotFoundError:
            print(f"📁 Fichier mémoire non trouvé: {fichier}")
            return False
        except Exception as e:
            print(f"⚠️ Erreur chargement mémoire: {e}")
            return False

# 🌟 INSTANCE GLOBALE JARVIS
jarvis_agent = JarvisAgentMemoireNaturelle()

def demarrer_jarvis_avec_memoire_naturelle():
    """Démarre JARVIS avec mémoire thermique naturelle"""
    print("🚀 Démarrage JARVIS avec Mémoire Thermique Naturelle")
    
    # 🔄 CHARGER LA MÉMOIRE EXISTANTE
    jarvis_agent.charger_memoire()
    
    # 📊 AFFICHER LES STATS
    stats = jarvis_agent.obtenir_stats_memoire()
    print(f"📊 Stats mémoire: {stats}")
    
    return jarvis_agent

if __name__ == "__main__":
    print("🤖 Test JARVIS Agent avec Mémoire Naturelle")
    
    # Démarrage
    agent = demarrer_jarvis_avec_memoire_naturelle()
    
    # Test de conversation
    reponse = agent.traiter_message_utilisateur("Bonjour JARVIS, comment vas-tu ?")
    print(f"🤖 JARVIS: {reponse}")
    
    # Test de réflexion autonome
    pensee = agent.forcer_reflexion_autonome()
    if pensee:
        print(f"💭 Pensée autonome: {pensee}")
    
    # Sauvegarde
    agent.sauvegarder_memoire()
