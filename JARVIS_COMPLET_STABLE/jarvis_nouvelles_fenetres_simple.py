#!/usr/bin/env python3
"""
🖼️ JARVIS NOUVELLES FENÊTRES SIMPLE - COMPATIBILITÉ
Fichier de compatibilité pour l'architecture multi-fenêtres
Créé pour Jean-Luc Passave avec intégration mémoire thermique naturelle
"""

import tkinter as tk
from tkinter import ttk, scrolledtext
import threading
import time
from datetime import datetime

# Import de la mémoire naturelle si disponible
try:
    from integration_memoire_naturelle_jarvis import (
        obtenir_stats_memoire_pour_interface,
        traiter_message_avec_memoire_naturelle
    )
    MEMOIRE_NATURELLE_DISPONIBLE = True
except ImportError:
    MEMOIRE_NATURELLE_DISPONIBLE = False

def get_all_new_interfaces():
    """Retourne toutes les nouvelles interfaces disponibles"""
    return {
        "security": "Interface Sécurité",
        "memory": "Interface Mémoire Thermique",
        "goap": "Interface GOAP Planner", 
        "cognitive": "Interface Raisonnement Cognitif",
        "creative": "Interface Créative"
    }

def create_security_interface():
    """Crée l'interface de sécurité"""
    try:
        window = tk.Toplevel()
        window.title("🔒 JARVIS - Interface Sécurité")
        window.geometry("600x400")
        window.configure(bg='#1a1a1a')
        
        # Label principal
        label = tk.Label(
            window, 
            text="🔒 INTERFACE SÉCURITÉ JARVIS",
            bg='#1a1a1a', 
            fg='#00ff00',
            font=('Courier', 14, 'bold')
        )
        label.pack(pady=10)
        
        # Zone de texte pour les logs de sécurité
        text_area = scrolledtext.ScrolledText(
            window,
            bg='#000000',
            fg='#00ff00', 
            font=('Courier', 10),
            height=20
        )
        text_area.pack(fill=tk.BOTH, expand=True, padx=10, pady=10)
        
        # Ajouter du contenu initial
        text_area.insert(tk.END, f"🔒 Interface Sécurité JARVIS initialisée\n")
        text_area.insert(tk.END, f"⏰ {datetime.now().strftime('%H:%M:%S')}\n")
        text_area.insert(tk.END, f"🧠 Mémoire Naturelle: {'✅ Activée' if MEMOIRE_NATURELLE_DISPONIBLE else '❌ Désactivée'}\n")
        
        return window
        
    except Exception as e:
        print(f"❌ Erreur création interface sécurité: {e}")
        return None

def create_memory_interface():
    """Crée l'interface de mémoire thermique"""
    try:
        window = tk.Toplevel()
        window.title("🧠 JARVIS - Mémoire Thermique Naturelle")
        window.geometry("800x600")
        window.configure(bg='#1a1a1a')
        
        # Label principal
        label = tk.Label(
            window,
            text="🧠 MÉMOIRE THERMIQUE NATURELLE JARVIS",
            bg='#1a1a1a',
            fg='#00ffff',
            font=('Courier', 14, 'bold')
        )
        label.pack(pady=10)
        
        # Frame pour les stats
        stats_frame = tk.Frame(window, bg='#1a1a1a')
        stats_frame.pack(fill=tk.X, padx=10, pady=5)
        
        # Zone de texte pour les stats mémoire
        stats_text = scrolledtext.ScrolledText(
            stats_frame,
            bg='#000000',
            fg='#00ffff',
            font=('Courier', 9),
            height=8
        )
        stats_text.pack(fill=tk.BOTH, expand=True)
        
        # Zone de texte pour le contexte cognitif
        context_label = tk.Label(
            window,
            text="💭 CONTEXTE COGNITIF ACTUEL",
            bg='#1a1a1a',
            fg='#ffff00',
            font=('Courier', 12, 'bold')
        )
        context_label.pack(pady=(10,5))
        
        context_text = scrolledtext.ScrolledText(
            window,
            bg='#000000',
            fg='#ffff00',
            font=('Courier', 9),
            height=15
        )
        context_text.pack(fill=tk.BOTH, expand=True, padx=10, pady=10)
        
        def update_memory_display():
            """Met à jour l'affichage de la mémoire"""
            if MEMOIRE_NATURELLE_DISPONIBLE:
                try:
                    # Obtenir les stats
                    stats = obtenir_stats_memoire_pour_interface()
                    
                    # Effacer et mettre à jour les stats
                    stats_text.delete(1.0, tk.END)
                    stats_text.insert(tk.END, f"📊 STATISTIQUES MÉMOIRE THERMIQUE\n")
                    stats_text.insert(tk.END, f"═══════════════════════════════\n")
                    
                    if 'stats_memoire' in stats:
                        mem_stats = stats['stats_memoire']
                        stats_text.insert(tk.END, f"🔥 Zone Hot: {mem_stats.get('zone_hot', 0)} entrées\n")
                        stats_text.insert(tk.END, f"🌡️ Zone Warm: {mem_stats.get('zone_warm', 0)} entrées\n")
                        stats_text.insert(tk.END, f"❄️ Zone Cold: {mem_stats.get('zone_cold', 0)} entrées\n")
                        stats_text.insert(tk.END, f"💭 Pensées autonomes: {mem_stats.get('pensees_autonomes', 0)}\n")
                        stats_text.insert(tk.END, f"🔗 Réflexes inconscients: {mem_stats.get('reflexes_inconscients', 0)}\n")
                        stats_text.insert(tk.END, f"🧠 Flux conscience: {'✅' if mem_stats.get('flux_conscience_actif', False) else '❌'}\n")
                    
                    # Mettre à jour le contexte cognitif
                    context_text.delete(1.0, tk.END)
                    context_text.insert(tk.END, f"💭 CONTEXTE COGNITIF JARVIS\n")
                    context_text.insert(tk.END, f"═══════════════════════════\n")
                    
                    if 'contexte_cognitif' in stats:
                        context_text.insert(tk.END, stats['contexte_cognitif'])
                    
                    if 'pensees_autonomes_recentes' in stats:
                        context_text.insert(tk.END, f"\n\n🤔 PENSÉES AUTONOMES RÉCENTES:\n")
                        for pensee in stats['pensees_autonomes_recentes']:
                            if isinstance(pensee, dict):
                                context_text.insert(tk.END, f"• {pensee.get('pensee', '')[:100]}...\n")
                    
                except Exception as e:
                    stats_text.delete(1.0, tk.END)
                    stats_text.insert(tk.END, f"⚠️ Erreur mise à jour mémoire: {e}\n")
            else:
                stats_text.delete(1.0, tk.END)
                stats_text.insert(tk.END, "❌ Mémoire Thermique Naturelle non disponible\n")
        
        # Mise à jour initiale
        update_memory_display()
        
        # Mise à jour automatique toutes les 10 secondes
        def auto_update():
            while True:
                try:
                    window.after(0, update_memory_display)
                    time.sleep(10)
                except:
                    break
        
        update_thread = threading.Thread(target=auto_update, daemon=True)
        update_thread.start()
        
        return window
        
    except Exception as e:
        print(f"❌ Erreur création interface mémoire: {e}")
        return None

def create_goap_planner_interface():
    """Crée l'interface GOAP Planner"""
    try:
        window = tk.Toplevel()
        window.title("🎯 JARVIS - GOAP Planner")
        window.geometry("700x500")
        window.configure(bg='#1a1a1a')
        
        label = tk.Label(
            window,
            text="🎯 GOAP PLANNER JARVIS",
            bg='#1a1a1a',
            fg='#ff8800',
            font=('Courier', 14, 'bold')
        )
        label.pack(pady=10)
        
        text_area = scrolledtext.ScrolledText(
            window,
            bg='#000000',
            fg='#ff8800',
            font=('Courier', 10),
            height=25
        )
        text_area.pack(fill=tk.BOTH, expand=True, padx=10, pady=10)
        
        text_area.insert(tk.END, f"🎯 GOAP Planner JARVIS initialisé\n")
        text_area.insert(tk.END, f"⏰ {datetime.now().strftime('%H:%M:%S')}\n")
        text_area.insert(tk.END, f"🧠 Planification autonome avec mémoire thermique\n")
        
        return window
        
    except Exception as e:
        print(f"❌ Erreur création interface GOAP: {e}")
        return None

def create_raisonnement_cognitif_interface():
    """Crée l'interface de raisonnement cognitif"""
    try:
        window = tk.Toplevel()
        window.title("🧠 JARVIS - Raisonnement Cognitif")
        window.geometry("800x600")
        window.configure(bg='#1a1a1a')
        
        label = tk.Label(
            window,
            text="🧠 RAISONNEMENT COGNITIF JARVIS",
            bg='#1a1a1a',
            fg='#ff00ff',
            font=('Courier', 14, 'bold')
        )
        label.pack(pady=10)
        
        text_area = scrolledtext.ScrolledText(
            window,
            bg='#000000',
            fg='#ff00ff',
            font=('Courier', 10),
            height=30
        )
        text_area.pack(fill=tk.BOTH, expand=True, padx=10, pady=10)
        
        text_area.insert(tk.END, f"🧠 Raisonnement Cognitif JARVIS initialisé\n")
        text_area.insert(tk.END, f"⏰ {datetime.now().strftime('%H:%M:%S')}\n")
        text_area.insert(tk.END, f"💭 Flux de conscience autonome avec mémoire naturelle\n")
        
        return window
        
    except Exception as e:
        print(f"❌ Erreur création interface raisonnement: {e}")
        return None

def create_creative_interface():
    """Crée l'interface créative"""
    try:
        window = tk.Toplevel()
        window.title("🎨 JARVIS - Interface Créative")
        window.geometry("700x500")
        window.configure(bg='#1a1a1a')
        
        label = tk.Label(
            window,
            text="🎨 INTERFACE CRÉATIVE JARVIS",
            bg='#1a1a1a',
            fg='#00ff88',
            font=('Courier', 14, 'bold')
        )
        label.pack(pady=10)
        
        text_area = scrolledtext.ScrolledText(
            window,
            bg='#000000',
            fg='#00ff88',
            font=('Courier', 10),
            height=25
        )
        text_area.pack(fill=tk.BOTH, expand=True, padx=10, pady=10)
        
        text_area.insert(tk.END, f"🎨 Interface Créative JARVIS initialisée\n")
        text_area.insert(tk.END, f"⏰ {datetime.now().strftime('%H:%M:%S')}\n")
        text_area.insert(tk.END, f"✨ Créativité augmentée par mémoire thermique naturelle\n")
        
        return window
        
    except Exception as e:
        print(f"❌ Erreur création interface créative: {e}")
        return None

if __name__ == "__main__":
    print("🖼️ Test des nouvelles fenêtres JARVIS")
    
    root = tk.Tk()
    root.withdraw()  # Cacher la fenêtre principale
    
    # Tester les interfaces
    security = create_security_interface()
    memory = create_memory_interface()
    
    if security and memory:
        print("✅ Interfaces créées avec succès")
        root.mainloop()
    else:
        print("❌ Erreur création interfaces")
