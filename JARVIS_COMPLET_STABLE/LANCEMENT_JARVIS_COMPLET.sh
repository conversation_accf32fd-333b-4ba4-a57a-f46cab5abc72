#!/bin/bash

# 🚀 SCRIPT DE LANCEMENT COMPLET JARVIS - JEAN-LUC PASSAVE
# Version stable et permanente - Protection contre destruction

echo "🚀 ================================"
echo "🤖 LANCEMENT JARVIS COMPLET STABLE"
echo "🚀 ================================"

# Définir le répertoire de base
BASE_DIR="/Volumes/seagate/Louna_Electron_Latest"
STABLE_DIR="$BASE_DIR/JARVIS_COMPLET_STABLE"

# Vérifier que nous sommes dans le bon répertoire
cd "$BASE_DIR" || {
    echo "❌ Erreur: Impossible d'accéder au répertoire $BASE_DIR"
    exit 1
}

# 1. DÉMARRER LE SERVEUR DEEPSEEK R1 8B DEPUIS JARVIS_COMPLET_STABLE
echo "🧠 Démarrage du serveur DeepSeek R1 8B depuis dossier unifié..."
echo "📁 Modèle: $STABLE_DIR/DeepSeek-R1-Distill-Llama-8B-Q4_K_M.gguf"
/opt/homebrew/Cellar/llama.cpp/5650/libexec/llama-server \
    --model "$STABLE_DIR/DeepSeek-R1-Distill-Llama-8B-Q4_K_M.gguf" \
    --host 0.0.0.0 \
    --port 8000 \
    --ctx-size 4096 \
    --threads 8 \
    --n-gpu-layers 32 \
    --batch-size 1024 \
    --ubatch-size 256 \
    --n-predict 2048 \
    --timeout 120 \
    --flash-attn \
    --mlock \
    --verbose &

LLAMA_PID=$!
echo "✅ Serveur DeepSeek R1 8B démarré (PID: $LLAMA_PID)"

# Attendre que le serveur soit prêt
echo "⏳ Attente du démarrage du serveur..."
sleep 10

# Vérifier que le serveur répond
for i in {1..30}; do
    if curl -s http://localhost:8000/health > /dev/null 2>&1; then
        echo "✅ Serveur DeepSeek R1 8B opérationnel"
        break
    fi
    echo "⏳ Attente du serveur... ($i/30)"
    sleep 2
done

# 2. DÉMARRER JARVIS DEPUIS DOSSIER UNIFIÉ
echo "🤖 Démarrage de JARVIS depuis dossier unifié..."
echo "📁 Dossier: $STABLE_DIR"
cd "$STABLE_DIR" || {
    echo "❌ Erreur: Impossible d'accéder au répertoire stable $STABLE_DIR"
    exit 1
}
python3 jarvis_architecture_multi_fenetres.py &

JARVIS_PID=$!
echo "✅ JARVIS démarré (PID: $JARVIS_PID)"

# 3. SAUVEGARDER LES PID POUR ARRÊT PROPRE
echo "$LLAMA_PID" > "$STABLE_DIR/llama_server.pid"
echo "$JARVIS_PID" > "$STABLE_DIR/jarvis.pid"

echo ""
echo "🎉 JARVIS COMPLET DÉMARRÉ AVEC SUCCÈS !"
echo "=================================================================================="
echo "💬 INTERFACE PRINCIPALE: http://localhost:7866"
echo "🧠 SERVEUR DEEPSEEK R1 8B: http://localhost:8000"
echo "=================================================================================="
echo "🔧 Pour arrêter JARVIS: ./ARRETER_JARVIS_COMPLET.sh"
echo "🔧 PIDs sauvegardés dans: $STABLE_DIR/"
echo "=================================================================================="

# Attendre les processus
wait
