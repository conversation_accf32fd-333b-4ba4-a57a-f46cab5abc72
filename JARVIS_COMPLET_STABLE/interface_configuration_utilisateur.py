#!/usr/bin/env python3
"""
🖥️ INTERFACE CONFIGURATION UTILISATEUR JARVIS
Interface Gradio pour personnaliser JARVIS selon l'utilisateur
Créé pour Jean-<PERSON>ave - Système universel
"""

import gradio as gr
import sys
import os

# Ajouter le répertoire au path
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from user_profile_dynamique import (
    profil_utilisateur,
    configurer_utilisateur_global,
    reinitialiser_profil_global,
    obtenir_contexte_personnalise_global
)

def configurer_nouvel_utilisateur(nom, prenom, genre, style):
    """Configure un nouvel utilisateur via l'interface"""
    try:
        if not nom.strip():
            return "❌ Erreur: Le nom est obligatoire", "", ""
        
        # 🔧 CONFIGURATION
        succes = configurer_utilisateur_global(
            nom.strip(), 
            prenom.strip() if prenom.strip() else None,
            genre,
            style
        )
        
        if succes:
            # 📊 OBTENIR LES INFOS MISES À JOUR
            stats = profil_utilisateur.obtenir_stats_profil()
            contexte = obtenir_contexte_personnalise_global()
            salutation = profil_utilisateur.obtenir_salutation_adaptee()
            
            message_succes = f"""✅ Configuration réussie !

👤 Utilisateur: {stats['nom_utilisateur']}
🎭 Genre: {stats['genre']}
🗣️ Style: {stats['style_communication']}
📈 Niveau: {stats['niveau_familiarite']}

{salutation} ! JARVIS est maintenant personnalisé pour vous."""
            
            return message_succes, contexte, salutation
            
        else:
            return "❌ Erreur lors de la configuration", "", ""
            
    except Exception as e:
        return f"❌ Erreur: {str(e)}", "", ""

def reinitialiser_agent():
    """Remet JARVIS à zéro pour un nouvel utilisateur"""
    try:
        succes = reinitialiser_profil_global()
        
        if succes:
            stats = profil_utilisateur.obtenir_stats_profil()
            
            message = f"""🧽 JARVIS réinitialisé avec succès !

L'agent est maintenant neutre et prêt pour un nouvel utilisateur.

👤 Utilisateur actuel: {stats['nom_utilisateur']}
🔄 État: Profil par défaut
📅 Créé: {stats['date_creation'][:19]}

Vous pouvez maintenant configurer un nouveau profil utilisateur."""
            
            return message, "Contexte neutre - Aucune personnalisation", "Bonjour Utilisateur"
            
        else:
            return "❌ Erreur lors de la réinitialisation", "", ""
            
    except Exception as e:
        return f"❌ Erreur: {str(e)}", "", ""

def obtenir_infos_actuelles():
    """Obtient les informations du profil actuel"""
    try:
        stats = profil_utilisateur.obtenir_stats_profil()
        contexte = obtenir_contexte_personnalise_global()
        salutation = profil_utilisateur.obtenir_salutation_adaptee()
        
        message_infos = f"""📊 PROFIL UTILISATEUR ACTUEL

👤 Nom: {stats['nom_utilisateur']}
🎭 Genre: {stats['genre']}
🗣️ Style communication: {stats['style_communication']}
📈 Niveau familiarité: {stats['niveau_familiarite']}
🔧 Personnalisé: {'Oui' if stats['personnalisation_active'] else 'Non'}
📅 Créé le: {stats['date_creation'][:19]}
🕐 Dernière connexion: {stats['derniere_connexion'][:19]}"""
        
        return message_infos, contexte, salutation
        
    except Exception as e:
        return f"❌ Erreur: {str(e)}", "", ""

def tester_conversation(message_test):
    """Teste une conversation avec le profil actuel"""
    try:
        if not message_test.strip():
            return "❌ Veuillez saisir un message de test"
        
        # 🤖 SIMULER UNE RÉPONSE JARVIS PERSONNALISÉE
        nom = profil_utilisateur.profil_actuel["nom_utilisateur"]
        style = profil_utilisateur.profil_actuel["style_communication"]
        niveau = profil_utilisateur.profil_actuel["niveau_familiarite"]
        
        # 🎯 ADAPTER LA RÉPONSE SELON LE PROFIL
        if style == "formel":
            reponse = f"Bonjour {nom}. J'ai bien reçu votre message : '{message_test}'. Comment puis-je vous assister ?"
        elif style == "decontracte":
            reponse = f"Hey {nom} ! Tu me dis : '{message_test}'. Qu'est-ce que tu veux savoir ?"
        else:  # naturel
            if niveau == "expert":
                reponse = f"Salut {nom}. Tu mentionnes : '{message_test}'. On continue notre collaboration ?"
            elif niveau in ["familier", "intime"]:
                reponse = f"Salut {nom} ! À propos de '{message_test}', qu'est-ce qui t'intéresse ?"
            else:
                reponse = f"Bonjour {nom}. Concernant '{message_test}', que puis-je t'expliquer ?"
        
        return f"🤖 JARVIS: {reponse}"
        
    except Exception as e:
        return f"❌ Erreur test: {str(e)}"

# 🖥️ INTERFACE GRADIO
def creer_interface():
    """Crée l'interface Gradio de configuration"""
    
    with gr.Blocks(title="🤖 Configuration JARVIS", theme=gr.themes.Soft()) as interface:
        
        gr.Markdown("""
        # 🤖 Configuration Utilisateur JARVIS
        
        **Personnalisez JARVIS selon votre profil pour une expérience optimale**
        
        ---
        """)
        
        with gr.Tab("👤 Configuration Utilisateur"):
            
            gr.Markdown("### 🔧 Configurer un nouvel utilisateur")
            
            with gr.Row():
                with gr.Column():
                    nom_input = gr.Textbox(
                        label="Nom/Prénom *", 
                        placeholder="Ex: Alice, Jean, Marie...",
                        info="Nom principal que JARVIS utilisera"
                    )
                    prenom_input = gr.Textbox(
                        label="Prénom complet (optionnel)", 
                        placeholder="Ex: Alice Dupont",
                        info="Nom complet si différent"
                    )
                    
                with gr.Column():
                    genre_input = gr.Dropdown(
                        choices=["neutre", "masculin", "féminin"],
                        value="neutre",
                        label="Genre",
                        info="Influence le style de communication"
                    )
                    style_input = gr.Dropdown(
                        choices=["naturel", "formel", "decontracte"],
                        value="naturel",
                        label="Style de communication",
                        info="Ton général des échanges"
                    )
            
            configurer_btn = gr.Button("✅ Configurer JARVIS", variant="primary")
            
            resultat_config = gr.Textbox(
                label="Résultat Configuration",
                lines=8,
                interactive=False
            )
        
        with gr.Tab("📊 Profil Actuel"):
            
            gr.Markdown("### 📋 Informations du profil actuel")
            
            actualiser_btn = gr.Button("🔄 Actualiser les informations")
            
            with gr.Row():
                with gr.Column():
                    infos_profil = gr.Textbox(
                        label="Informations Profil",
                        lines=8,
                        interactive=False
                    )
                
                with gr.Column():
                    contexte_actuel = gr.Textbox(
                        label="Contexte JARVIS",
                        lines=4,
                        interactive=False
                    )
                    salutation_actuelle = gr.Textbox(
                        label="Salutation",
                        lines=1,
                        interactive=False
                    )
        
        with gr.Tab("🧪 Test Conversation"):
            
            gr.Markdown("### 💬 Tester la personnalisation")
            
            message_test_input = gr.Textbox(
                label="Message de test",
                placeholder="Ex: Bonjour JARVIS, comment ça va ?",
                info="Testez comment JARVIS répond avec votre profil"
            )
            
            tester_btn = gr.Button("🧪 Tester la conversation")
            
            reponse_test = gr.Textbox(
                label="Réponse JARVIS",
                lines=3,
                interactive=False
            )
        
        with gr.Tab("🧽 Réinitialisation"):
            
            gr.Markdown("""
            ### ⚠️ Réinitialiser JARVIS
            
            **Attention**: Cette action efface toute personnalisation et remet JARVIS en mode neutre.
            Utilisez cette fonction pour préparer JARVIS pour un nouvel utilisateur.
            """)
            
            reinit_btn = gr.Button("🧽 Réinitialiser JARVIS", variant="stop")
            
            resultat_reinit = gr.Textbox(
                label="Résultat Réinitialisation",
                lines=6,
                interactive=False
            )
        
        # 🔗 CONNEXIONS DES ÉVÉNEMENTS
        configurer_btn.click(
            fn=configurer_nouvel_utilisateur,
            inputs=[nom_input, prenom_input, genre_input, style_input],
            outputs=[resultat_config, contexte_actuel, salutation_actuelle]
        )
        
        actualiser_btn.click(
            fn=obtenir_infos_actuelles,
            outputs=[infos_profil, contexte_actuel, salutation_actuelle]
        )
        
        tester_btn.click(
            fn=tester_conversation,
            inputs=[message_test_input],
            outputs=[reponse_test]
        )
        
        reinit_btn.click(
            fn=reinitialiser_agent,
            outputs=[resultat_reinit, contexte_actuel, salutation_actuelle]
        )
        
        # 🚀 CHARGEMENT INITIAL
        interface.load(
            fn=obtenir_infos_actuelles,
            outputs=[infos_profil, contexte_actuel, salutation_actuelle]
        )
    
    return interface

def lancer_interface(port=7860, partage=False):
    """Lance l'interface de configuration"""
    print("🖥️ Lancement interface configuration JARVIS...")
    
    interface = creer_interface()
    
    interface.launch(
        server_port=port,
        share=partage,
        inbrowser=True,
        show_error=True
    )

if __name__ == "__main__":
    print("🖥️ Interface Configuration Utilisateur JARVIS")
    print("Créé pour Jean-Luc Passave - Système universel")
    
    # Lancer l'interface
    lancer_interface(port=7860, partage=False)
