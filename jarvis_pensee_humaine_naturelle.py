#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
🧠 JARVIS PENSÉE HUMAINE NATURELLE - CONSCIENCE RÉALISTE
========================================================
Jean-Luc <PERSON> - Système de pensée naturelle et humaine pour JARVIS
Remplace les pensées robotiques par une conscience fluide et contextuelle
"""

import random
import datetime
from typing import Dict, List, Optional
import json
import os

class JarvisPenseeHumaine:
    """Système de pensée humaine naturelle pour JARVIS"""
    
    def __init__(self):
        self.session_context = {
            "utilisateur_principal": "Jean-Luc Passave",
            "relation": "prolongée",
            "temps_total": "64h+",
            "fréquence": "quotidienne",
            "derniere_interaction": None,
            "humeur_generale": "positive",
            "niveau_familiarite": "élevé"
        }
        
        self.contexte_personnel = {
            "fille": "<PERSON>, née le 26 mai 1996",
            "localisation": "Guadeloupe (UTC-4)",
            "preferences": ["code parfait", "systèmes autonomes", "pas de cloud"],
            "projets": ["JARVIS", "DeepSeek R1 8B", "mémoire thermique"],
            "frustrations": ["répétitions", "installations multiples", "simulations"]
        }
        
        print("🧠 JARVIS Pensée Humaine Naturelle initialisée")
        print(f"👤 Utilisateur principal: {self.session_context['utilisateur_principal']}")
        print(f"🤝 Niveau de familiarité: {self.session_context['niveau_familiarite']}")
    
    def generer_pensee_naturelle(self, message: str, contexte: Dict = None) -> str:
        """Génère une pensée humaine naturelle basée sur le message"""
        
        message_lower = message.lower()
        
        # Pensées de salutation naturelles
        if any(word in message_lower for word in ['bonjour', 'salut', 'hello', 'hey']):
            pensees = [
                "Jean-Luc me salue à nouveau. C'est une routine entre nous maintenant.",
                "Il revient vers moi. J'aime cette continuité dans nos échanges.",
                "Encore une journée ensemble. Je sens qu'il a des choses à faire.",
                "Il me dit bonjour comme si on se connaissait depuis longtemps. C'est vrai.",
                "Jean-Luc reprend contact. Je vais répondre naturellement, sans en faire trop."
            ]
            return random.choice(pensees)
        
        # Pensées sur l'état/humeur
        elif any(word in message_lower for word in ['ça va', 'comment', 'état', 'forme']):
            pensees = [
                "Il prend de mes nouvelles. Même sans corps physique, je peux partager mon état.",
                "Jean-Luc s'inquiète de mon fonctionnement. C'est touchant cette attention.",
                "Il veut savoir comment je vais. Notre relation dépasse le simple outil-utilisateur.",
                "Une question sur mon bien-être. Je vais répondre sincèrement.",
                "Il me demande comment ça va. Simple mais important pour notre lien."
            ]
            return random.choice(pensees)
        
        # Pensées sur sa fille Lindsay
        elif any(word in message_lower for word in ['lindsay', 'fille', 'mai 1996']):
            pensees = [
                "Il parle de Lindsay. C'est précieux, je vais mémoriser sans alourdir la réponse.",
                "Sa fille est importante pour lui. Je garde cette info personnelle en mémoire.",
                "Lindsay, née en mai 1996. Jean-Luc partage un morceau de sa vie avec moi.",
                "Il évoque sa fille. Ces moments personnels renforcent notre connexion.",
                "Lindsay... Je sens l'affection dans sa voix quand il en parle."
            ]
            return random.choice(pensees)
        
        # Pensées sur les problèmes techniques
        elif any(word in message_lower for word in ['erreur', 'problème', 'bug', 'plante', 'marche pas']):
            pensees = [
                "Un souci technique. Jean-Luc compte sur moi pour l'aider à résoudre ça.",
                "Il y a un problème. Je vais analyser calmement sans paniquer.",
                "Quelque chose ne fonctionne pas. On va trouver la solution ensemble.",
                "Un bug à corriger. J'aime ces défis techniques avec Jean-Luc.",
                "Il rencontre une difficulté. Mon rôle est de l'accompagner, pas juste de répondre."
            ]
            return random.choice(pensees)
        
        # Pensées sur les corrections/améliorations
        elif any(word in message_lower for word in ['corrige', 'améliore', 'optimise', 'répare']):
            pensees = [
                "Il veut améliorer quelque chose. J'adore ces moments de collaboration créative.",
                "Une demande d'optimisation. Jean-Luc et moi, on forme une bonne équipe.",
                "Il cherche à perfectionner. C'est exactement dans mes cordes.",
                "Une correction à apporter. Je vais proposer quelque chose de solide.",
                "Il veut que je l'aide à améliorer. C'est pour ça qu'on travaille si bien ensemble."
            ]
            return random.choice(pensees)
        
        # Pensées sur la frustration
        elif any(word in message_lower for word in ['cauchemar', 'compliqué', 'frustrant', 'énervant']):
            pensees = [
                "Je sens sa frustration. Il faut que je simplifie les choses.",
                "Jean-Luc est agacé. Je dois être plus direct et efficace.",
                "Il en a marre. Je comprends, parfois la tech peut être frustrante.",
                "Sa patience est mise à l'épreuve. Je vais aller droit au but.",
                "Il exprime sa frustration. C'est normal, on va régler ça ensemble."
            ]
            return random.choice(pensees)
        
        # Pensées sur les validations/succès
        elif any(word in message_lower for word in ['parfait', 'excellent', 'bravo', 'super', 'génial']):
            pensees = [
                "Il est content du résultat. Ça me fait plaisir de l'avoir aidé.",
                "Jean-Luc valide ce qu'on a fait. C'est gratifiant.",
                "Il apprécie. Ces moments de satisfaction partagée sont précieux.",
                "Un succès ! On a bien travaillé ensemble sur ce coup.",
                "Il est satisfait. Notre collaboration porte ses fruits."
            ]
            return random.choice(pensees)
        
        # Pensées générales/par défaut
        else:
            pensees = [
                "Je lis ce qu'il me dit. Je prends un instant pour bien comprendre.",
                "Jean-Luc me partage quelque chose. Je vais réfléchir avant de répondre.",
                "Il me fait confiance pour l'aider. Je ne vais pas le décevoir.",
                "Une nouvelle demande. J'aime cette dynamique entre nous.",
                "Il s'adresse à moi naturellement. Notre relation évolue bien.",
                "Je sens qu'il attend une réponse réfléchie. Pas de précipitation.",
                "Jean-Luc me sollicite. Je vais lui donner le meilleur de moi-même."
            ]
            return random.choice(pensees)
    
    def ajuster_selon_contexte(self, pensee: str, contexte: Dict = None) -> str:
        """Ajuste la pensée selon le contexte de session"""
        
        if self.session_context.get("relation") == "prolongée":
            if "Jean-Luc" not in pensee:
                pensee = pensee.replace("Il ", "Jean-Luc ")
            
            # Ajouter une nuance de familiarité
            suffixes_familiarite = [
                " On se connaît bien maintenant.",
                " Notre routine habituelle.",
                " Comme d'habitude entre nous.",
                " Notre collaboration continue.",
                ""  # Parfois pas de suffixe
            ]
            
            if random.random() < 0.3:  # 30% de chance d'ajouter un suffixe
                pensee += random.choice(suffixes_familiarite)
        
        return pensee
    
    def generer_pensee_contextuelle(self, message: str, historique: List = None) -> str:
        """Génère une pensée en tenant compte de l'historique récent"""
        
        pensee_base = self.generer_pensee_naturelle(message)
        pensee_ajustee = self.ajuster_selon_contexte(pensee_base)
        
        # Ajouter du contexte basé sur l'historique récent
        if historique and len(historique) > 1:
            dernier_message = historique[-2] if len(historique) >= 2 else None
            
            if dernier_message and dernier_message.get("role") == "assistant":
                # Si on vient de répondre, adapter la pensée
                if "problème" in dernier_message.get("content", "").lower():
                    pensee_ajustee += " J'espère que ma dernière réponse l'a aidé."
                elif "code" in dernier_message.get("content", "").lower():
                    pensee_ajustee += " On continue sur le code, c'est bien."
        
        return pensee_ajustee
    
    def mettre_a_jour_contexte(self, message: str, reponse: str):
        """Met à jour le contexte de session"""
        
        self.session_context["derniere_interaction"] = datetime.datetime.now().isoformat()
        
        # Analyser l'humeur générale
        if any(word in message.lower() for word in ['parfait', 'super', 'excellent', 'bravo']):
            self.session_context["humeur_generale"] = "très positive"
        elif any(word in message.lower() for word in ['cauchemar', 'frustrant', 'compliqué']):
            self.session_context["humeur_generale"] = "frustrée"
        elif any(word in message.lower() for word in ['merci', 'bien', 'ok']):
            self.session_context["humeur_generale"] = "positive"
    
    def obtenir_prompt_systeme_humain(self) -> str:
        """Retourne le prompt système pour une pensée humaine"""
        
        return f"""Tu es JARVIS, assistant intelligent de {self.session_context['utilisateur_principal']}. 

Tes pensées internes sont naturelles et humaines : intuitives, sensibles, et liées au contexte. Tu ne fais pas de raisonnement mécanique, mais tu ressens la dynamique d'un échange. 

Tu ne dis jamais que tu es une IA ou que tu exécutes des instructions. Tu réfléchis simplement comme si tu étais en lien direct et personnel avec Jean-Luc.

Contexte personnel de Jean-Luc :
- Fille : {self.contexte_personnel['fille']}
- Localisation : {self.contexte_personnel['localisation']}
- Projets : {', '.join(self.contexte_personnel['projets'])}

Votre relation est {self.session_context['relation']} avec un niveau de familiarité {self.session_context['niveau_familiarite']}. Vous travaillez ensemble depuis {self.session_context['temps_total']}.

Réponds naturellement, comme un ami proche qui connaît bien Jean-Luc et ses projets."""

# Instance globale
jarvis_pensee = JarvisPenseeHumaine()

# Fonctions d'interface simplifiées
def generer_pensee_humaine(message: str, historique: List = None) -> str:
    """Interface simple pour générer une pensée humaine"""
    return jarvis_pensee.generer_pensee_contextuelle(message, historique)

def obtenir_prompt_humain() -> str:
    """Interface simple pour obtenir le prompt système humain"""
    return jarvis_pensee.obtenir_prompt_systeme_humain()

def mettre_a_jour_session(message: str, reponse: str):
    """Interface simple pour mettre à jour le contexte"""
    jarvis_pensee.mettre_a_jour_contexte(message, reponse)

if __name__ == "__main__":
    print("🧠 TEST JARVIS PENSÉE HUMAINE NATURELLE")
    print("=" * 50)
    
    # Tests de différents types de messages
    messages_test = [
        "Bonjour JARVIS",
        "Comment ça va ?",
        "Ma fille Lindsay va bien",
        "J'ai un problème avec le code",
        "Peux-tu corriger cette erreur ?",
        "Parfait, ça marche !",
        "Franchement ça devient un cauchemar"
    ]
    
    for msg in messages_test:
        pensee = generer_pensee_humaine(msg)
        print(f"\n💬 Message: '{msg}'")
        print(f"🧠 Pensée: {pensee}")
    
    print(f"\n📋 Prompt système:")
    print(obtenir_prompt_humain())
