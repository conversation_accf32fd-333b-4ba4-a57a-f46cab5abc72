#!/usr/bin/env python3
"""
🔗 INTÉGRATION MÉMOIRE THERMIQUE NATURELLE DANS JARVIS EXISTANT
Script d'intégration pour Jean-Luc Passave
Connecte la nouvelle approche révolutionnaire à l'architecture existante
"""

import sys
import os
import time
from datetime import datetime
from jarvis_agent_avec_memoire_naturelle import jarvis_agent, demarrer_jarvis_avec_memoire_naturelle

class IntegrationMemoireNaturelle:
    """
    INTÉGRATEUR POUR CONNECTER LA MÉMOIRE NATURELLE À L'INTERFACE EXISTANTE
    """
    
    def __init__(self):
        self.agent_memoire_naturelle = None
        self.interface_connectee = False
        self.mode_debug = True
        
        print("🔗 Intégrateur Mémoire Naturelle initialisé")

    def connecter_a_interface_existante(self):
        """
        Connecte la mémoire naturelle à l'interface JARVIS existante
        """
        try:
            # 🚀 DÉMARRER L'AGENT AVEC MÉMOIRE NATURELLE
            self.agent_memoire_naturelle = demarrer_jarvis_avec_memoire_naturelle()
            
            # 🔗 VÉRIFIER LA CONNEXION VLLM
            if self._tester_connexion_vllm():
                print("✅ Connexion VLLM/DeepSeek R1 8B confirmée")
            else:
                print("⚠️ Connexion VLLM non disponible - Mode dégradé")
            
            self.interface_connectee = True
            print("🔗 Interface connectée avec succès")
            
            return True
            
        except Exception as e:
            print(f"❌ Erreur connexion interface: {e}")
            return False

    def _tester_connexion_vllm(self) -> bool:
        """Teste la connexion VLLM"""
        try:
            import requests
            response = requests.get("http://localhost:8000/health", timeout=5)
            return response.status_code == 200
        except:
            return False

    def traiter_message_interface(self, message: str, agent_type: str = "agent1") -> str:
        """
        POINT D'ENTRÉE PRINCIPAL POUR L'INTERFACE EXISTANTE
        Remplace la fonction de traitement de message existante
        """
        if not self.interface_connectee or not self.agent_memoire_naturelle:
            return "❌ Interface mémoire naturelle non connectée"
        
        try:
            # 📝 LOG DEBUG
            if self.mode_debug:
                print(f"🔄 Traitement message avec mémoire naturelle: {message[:50]}...")
            
            # 🧠 TRAITEMENT AVEC MÉMOIRE NATURELLE INTÉGRÉE
            reponse = self.agent_memoire_naturelle.traiter_message_utilisateur(message, agent_type)
            
            # 📊 STATS MÉMOIRE (DEBUG)
            if self.mode_debug:
                stats = self.agent_memoire_naturelle.obtenir_stats_memoire()
                print(f"📊 Stats mémoire post-traitement: {stats}")
            
            return reponse
            
        except Exception as e:
            print(f"⚠️ Erreur traitement message: {e}")
            return f"Erreur technique: {str(e)}"

    def obtenir_contexte_pour_interface(self) -> dict:
        """
        Fournit le contexte mémoire pour l'interface utilisateur
        """
        if not self.agent_memoire_naturelle:
            return {}
        
        try:
            stats = self.agent_memoire_naturelle.obtenir_stats_memoire()
            
            # 🧠 CONTEXTE COGNITIF VISIBLE
            contexte_cognitif = self.agent_memoire_naturelle.memoire.fournir_contexte_cognitif()
            
            # 💭 DERNIÈRES PENSÉES AUTONOMES
            pensees_recentes = list(self.agent_memoire_naturelle.memoire.pensees_autonomes)[-3:]
            
            return {
                "stats_memoire": stats,
                "contexte_cognitif": contexte_cognitif,
                "pensees_autonomes_recentes": pensees_recentes,
                "flux_conscience_actif": self.agent_memoire_naturelle.memoire.flux_conscience_actif,
                "derniere_interaction": self.agent_memoire_naturelle.derniere_interaction,
                "timestamp": datetime.now().isoformat()
            }
            
        except Exception as e:
            print(f"⚠️ Erreur obtention contexte: {e}")
            return {"erreur": str(e)}

    def activer_mode_turbo(self, actif: bool = True):
        """Interface pour activer le mode turbo"""
        if self.agent_memoire_naturelle:
            self.agent_memoire_naturelle.activer_mode_turbo(actif)

    def gerer_flux_conscience(self, actif: bool = True):
        """Interface pour gérer le flux de conscience"""
        if self.agent_memoire_naturelle:
            self.agent_memoire_naturelle.gerer_flux_conscience(actif)

    def rechercher_memoire(self, query: str) -> list:
        """Interface pour rechercher dans la mémoire"""
        if self.agent_memoire_naturelle:
            return self.agent_memoire_naturelle.rechercher_dans_memoire(query)
        return []

    def forcer_sauvegarde_memoire(self) -> bool:
        """Force la sauvegarde de la mémoire"""
        if self.agent_memoire_naturelle:
            return self.agent_memoire_naturelle.sauvegarder_memoire()
        return False

    def generer_rapport_memoire(self) -> str:
        """Génère un rapport détaillé de la mémoire"""
        if not self.agent_memoire_naturelle:
            return "❌ Agent non disponible"
        
        try:
            stats = self.agent_memoire_naturelle.obtenir_stats_memoire()
            contexte = self.agent_memoire_naturelle.memoire.fournir_contexte_cognitif()
            
            rapport = f"""
🧠 RAPPORT MÉMOIRE THERMIQUE NATURELLE JARVIS
═══════════════════════════════════════════════

📊 STATISTIQUES GÉNÉRALES:
• Zone Hot (pensées immédiates): {stats.get('zone_hot', 0)} entrées
• Zone Warm (souvenirs actifs): {stats.get('zone_warm', 0)} entrées  
• Zone Cold (mémoire profonde): {stats.get('zone_cold', 0)} entrées
• Pensées autonomes générées: {stats.get('pensees_autonomes', 0)}
• Réflexes inconscients: {stats.get('reflexes_inconscients', 0)}

🔄 ÉTAT DU FLUX DE CONSCIENCE:
• Flux actif: {stats.get('flux_conscience_actif', False)}
• Dernière réflexion: {datetime.fromtimestamp(stats.get('derniere_reflexion', 0)).strftime('%H:%M:%S')}

🧠 CONTEXTE COGNITIF ACTUEL:
{contexte[:500]}...

⏰ Rapport généré: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}
"""
            return rapport
            
        except Exception as e:
            return f"❌ Erreur génération rapport: {e}"

# 🌟 INSTANCE GLOBALE D'INTÉGRATION
integrateur_memoire = IntegrationMemoireNaturelle()

def initialiser_memoire_naturelle_jarvis():
    """
    FONCTION D'INITIALISATION PRINCIPALE
    À appeler depuis l'interface JARVIS existante
    """
    print("🚀 Initialisation Mémoire Thermique Naturelle JARVIS")
    
    if integrateur_memoire.connecter_a_interface_existante():
        print("✅ Mémoire thermique naturelle intégrée avec succès")
        
        # 📊 RAPPORT INITIAL
        rapport = integrateur_memoire.generer_rapport_memoire()
        print(rapport)
        
        return integrateur_memoire
    else:
        print("❌ Échec de l'intégration")
        return None

def traiter_message_avec_memoire_naturelle(message: str, agent_type: str = "agent1") -> str:
    """
    FONCTION DE TRAITEMENT PRINCIPAL
    Remplace la fonction existante dans l'interface JARVIS
    """
    return integrateur_memoire.traiter_message_interface(message, agent_type)

def obtenir_stats_memoire_pour_interface() -> dict:
    """
    FONCTION POUR L'INTERFACE UTILISATEUR
    Fournit les stats de mémoire pour affichage
    """
    return integrateur_memoire.obtenir_contexte_pour_interface()

def activer_turbo_jarvis(actif: bool = True):
    """Active/désactive le mode turbo"""
    integrateur_memoire.activer_mode_turbo(actif)

def gerer_conscience_jarvis(actif: bool = True):
    """Gère le flux de conscience"""
    integrateur_memoire.gerer_flux_conscience(actif)

# 🔧 FONCTIONS DE COMPATIBILITÉ AVEC L'INTERFACE EXISTANTE
def get_agent_response(message: str, agent_type: str = "agent1") -> str:
    """Fonction de compatibilité avec l'interface existante"""
    return traiter_message_avec_memoire_naturelle(message, agent_type)

def get_memory_context() -> dict:
    """Fonction de compatibilité pour le contexte mémoire"""
    return obtenir_stats_memoire_pour_interface()

if __name__ == "__main__":
    print("🔗 Test Intégration Mémoire Naturelle JARVIS")
    
    # Initialisation
    integrateur = initialiser_memoire_naturelle_jarvis()
    
    if integrateur:
        # Test de traitement de message
        print("\n🧪 Test traitement message:")
        reponse = traiter_message_avec_memoire_naturelle("Bonjour JARVIS, peux-tu me parler de tes capacités ?")
        print(f"🤖 Réponse: {reponse}")
        
        # Test des stats
        print("\n📊 Test stats mémoire:")
        stats = obtenir_stats_memoire_pour_interface()
        print(f"Stats: {stats}")
        
        # Test mode turbo
        print("\n🚀 Test mode turbo:")
        activer_turbo_jarvis(True)
        reponse_turbo = traiter_message_avec_memoire_naturelle("Réponse rapide ?")
        print(f"🤖 Réponse turbo: {reponse_turbo}")
        
        # Rapport final
        print("\n📋 Rapport final:")
        rapport = integrateur.generer_rapport_memoire()
        print(rapport)
    
    else:
        print("❌ Échec des tests")
